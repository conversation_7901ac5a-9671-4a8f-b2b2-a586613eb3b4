// ==UserScript==
// @name         Youtube Audio HD
// @namespace    https://tampermonkey.com/
// @version      5.1
// @description  HD audio dengan bass jernih, stereo seimbang, dan pengaturan tersimpan otomatis.
// @icon         https://www.google.com/s2/favicons?sz=64&domain=youtube.com
// <AUTHOR>
// @match        https://www.youtube.com/*
// @match        https://music.youtube.com/*
// @match        https://m.youtube.com/*
// @match        https://www.youtube-nocookie.com/*
// @license      MIT
// @grant        GM_getValue
// @grant        GM_setValue
// @grant        GM_registerMenuCommand
// ==/UserScript==
(function () {
  "use strict";

  // Enhanced Audio Constants - Optimized for Crystal Clear Sound with Deep Bass
  const DEFAULT_DELAY = 0.012;
  const DEFAULT_GAIN = 1.15;
  const DEFAULT_THRESHOLD = -18;
  const DEFAULT_RATIO = 3;
  const DEFAULT_ATTACK = 0.002;
  const DEFAULT_RELEASE = 0.08;
  const DEFAULT_REVERB_WET = 0.18;
  const DEFAULT_BASS_BOOST = 1.8;          // Enhanced for small bass clarity
  const DEFAULT_SUB_BASS_BOOST = 2.2;      // New: Deep sub-bass enhancement
  const DEFAULT_MID_BASS_BOOST = 1.6;      // New: Mid-bass punch
  const DEFAULT_TREBLE_BOOST = 1.3;
  const DEFAULT_PRESENCE_BOOST = 1.25;
  const DEFAULT_STEREO_WIDTH = 1.0;        // Balanced stereo - no widening for clear vocals
  const DEFAULT_STEREO_BALANCE = 0.0;      // New: Center balance (-1.0 = left, 0.0 = center, 1.0 = right)
  const DEFAULT_BASS_COMPRESSOR_RATIO = 3; // New: Bass-specific compression
  const DEFAULT_HARMONIC_INTENSITY = 0.4;    // New: Harmonic enhancement
  const DEFAULT_DYNAMIC_RANGE = 1.2;         // New: Dynamic range expansion
  const DEFAULT_PSYCHO_BASS = 0.5;           // New: Psychoacoustic bass
  const DEFAULT_STEREO_DEPTH = 0.3;          // New: Enhanced stereo depth

  const SELECTORS = {
    VIDEO: "video.html5-main-video",
    FALLBACK_VIDEO: "video"
  };

  const RETRY_CONFIG = {
    MAX_ATTEMPTS: 10,
    INITIAL_DELAY: 100,
    MAX_DELAY: 2000
  };

  // Audio processor class for better organization and performance
  class AudioProcessor {
    constructor() {
      this.audioCtx = null;
      this.delayNode = null;
      this.gainNode = null;
      this.compressorNode = null;
      this.source = null;
      this.currentVideo = null;
      this.isInitialized = false;
      this.eventListeners = new Map();

      // Enhanced Audio Processing Nodes
      this.reverbNode = null;
      this.reverbGainNode = null;
      this.dryGainNode = null;

      // Multi-band Bass Enhancement System
      this.subBassFilterNode = null;      // 20-60Hz - Deep sub-bass
      this.midBassFilterNode = null;      // 60-150Hz - Mid-bass punch
      this.bassFilterNode = null;         // 150-300Hz - Upper bass
      this.bassCompressorNode = null;     // Bass-specific compressor

      // Clarity Enhancement
      this.trebleFilterNode = null;
      this.presenceFilterNode = null;     // Essential untuk vocal clarity
      this.clarityFilterNode = null;      // New: High-frequency clarity

      // Balanced Stereo Processing
      this.splitterNode = null;
      this.mergerNode = null;
      this.leftGainNode = null;        // New: Left channel gain control
      this.rightGainNode = null;       // New: Right channel gain control
      this.leftDelayNode = null;
      this.rightDelayNode = null;

      // New Audio Enhancement Engines
      this.harmonicNode = null;         // Harmonic enhancement
      this.dynamicNode = null;          // Dynamic range expansion  
      this.psychoBassNode = null;       // Psychoacoustic bass
      this.stereoDepthNode = null;      // Enhanced stereo imaging

      // Optimized settings dengan validasi
      this.settings = this.loadValidatedSettings();

      // Initialize timeout reference for debouncing
      this.updateTimeout = null;
    }

    // Load dan validasi settings dari storage
    loadValidatedSettings() {
      const defaultSettings = {
        delayTime: DEFAULT_DELAY,
        gainValue: DEFAULT_GAIN,
        threshold: DEFAULT_THRESHOLD,
        ratio: DEFAULT_RATIO,
        attack: DEFAULT_ATTACK,
        release: DEFAULT_RELEASE,
        reverbWet: DEFAULT_REVERB_WET,

        // Enhanced Bass System
        subBassBoost: DEFAULT_SUB_BASS_BOOST,
        midBassBoost: DEFAULT_MID_BASS_BOOST,
        bassBoost: DEFAULT_BASS_BOOST,
        bassCompressorRatio: DEFAULT_BASS_COMPRESSOR_RATIO,

        // Clarity Enhancement
        trebleBoost: DEFAULT_TREBLE_BOOST,
        presenceBoost: DEFAULT_PRESENCE_BOOST,

        // Balanced Stereo Enhancement
        stereoWidth: DEFAULT_STEREO_WIDTH,
        stereoBalance: DEFAULT_STEREO_BALANCE,

        // New Enhancements
        harmonicIntensity: DEFAULT_HARMONIC_INTENSITY,
        dynamicRange: DEFAULT_DYNAMIC_RANGE,
        psychoBass: DEFAULT_PSYCHO_BASS,
        stereoDepth: DEFAULT_STEREO_DEPTH
      };

      const validationRules = {
        delayTime: { min: 0.001, max: 0.1 },
        gainValue: { min: 0.1, max: 3.0 },
        threshold: { min: -100, max: 0 },
        ratio: { min: 1, max: 20 },
        attack: { min: 0.001, max: 1 },
        release: { min: 0.01, max: 1 },
        reverbWet: { min: 0, max: 1 },

        // Enhanced Bass Validation
        subBassBoost: { min: 0.5, max: 4.0 },
        midBassBoost: { min: 0.5, max: 3.5 },
        bassBoost: { min: 0.5, max: 3.0 },
        bassCompressorRatio: { min: 1, max: 10 },

        // Clarity Validation
        trebleBoost: { min: 0.5, max: 3.0 },
        presenceBoost: { min: 0.5, max: 3.0 },

        // Balanced Stereo Validation
        stereoWidth: { min: 0.5, max: 2.0 },      // Reduced max for balanced audio
        stereoBalance: { min: -1.0, max: 1.0 },    // New: Balance control

        // New Enhancements Validation
        harmonicIntensity: { min: 0, max: 1 },
        dynamicRange: { min: 1, max: 2 },
        psychoBass: { min: 0, max: 1 },
        stereoDepth: { min: 0, max: 1 }
      };

      const settings = {};
      let hasInvalidValues = false;

      Object.entries(defaultSettings).forEach(([key, defaultValue]) => {
        const storedValue = GM_getValue(key, defaultValue);
        const rule = validationRules[key];

        // Validasi nilai
        if (typeof storedValue === 'number' &&
            isFinite(storedValue) &&
            storedValue >= rule.min &&
            storedValue <= rule.max) {
          settings[key] = storedValue;
        } else {
          console.warn(`Invalid stored value for ${key}: ${storedValue}, using default: ${defaultValue}`);
          settings[key] = defaultValue;
          GM_setValue(key, defaultValue); // Save corrected value
          hasInvalidValues = true;
        }
      });

      if (hasInvalidValues) {
        this.showNotification('Some settings were corrected', 'info');
      }

      return settings;
    }

    // Main initialization method
    async init() {
      try {

        const videoElement = await this.findVideoElement();
        if (!videoElement) {
          throw new Error('No video element found');
        }

        await this.initializeAudioNodes();
        await this.connectAudio(videoElement);
        this.setupVideoEventListeners(videoElement);

        // Show enhanced status indicator and success notification
        this.createStatusIndicator();
        this.showNotification('🎵 Enhanced Bass + Balanced Stereo Audio Activated! 🔊', 'success');

        console.log('Audio processor initialized successfully');
      } catch (error) {
        console.error('Failed to initialize audio processor:', error);
        throw error;
      }
    }

    // Optimized video element detection with exponential backoff
    async findVideoElement(attempt = 1) {
      const videoElement = document.querySelector(SELECTORS.VIDEO) ||
                          document.querySelector(SELECTORS.FALLBACK_VIDEO);

      if (videoElement) {
        return videoElement;
      }

      if (attempt >= RETRY_CONFIG.MAX_ATTEMPTS) {
        throw new Error("Video element not found after maximum attempts");
      }

      const delay = Math.min(
        RETRY_CONFIG.INITIAL_DELAY * Math.pow(2, attempt - 1),
        RETRY_CONFIG.MAX_DELAY
      );

      await new Promise(resolve => setTimeout(resolve, delay));
      return this.findVideoElement(attempt + 1);
    }

    // Removed duplicate initAudioContext method - functionality merged into initializeAudioNodes

    async initializeAudioNodes() {
      try {
        if (!this.audioCtx) {
          const AudioContextClass = window.AudioContext || window['webkitAudioContext'];
          this.audioCtx = new AudioContextClass();
        }

        // Create basic audio processing nodes
        this.delayNode = this.audioCtx.createDelay();
        this.gainNode = this.audioCtx.createGain();
        this.compressorNode = this.audioCtx.createDynamicsCompressor();

        // Create balanced stereo processing nodes
        this.splitterNode = this.audioCtx.createChannelSplitter(2);
        this.mergerNode = this.audioCtx.createChannelMerger(2);
        this.leftGainNode = this.audioCtx.createGain();   // New: Left channel gain control
        this.rightGainNode = this.audioCtx.createGain();  // New: Right channel gain control
        this.leftDelayNode = this.audioCtx.createDelay();
        this.rightDelayNode = this.audioCtx.createDelay();

        // === ENHANCED MULTI-BAND BASS SYSTEM ===
        // Sub-bass filter (20-60Hz) - Makes deep bass audible
        this.subBassFilterNode = this.audioCtx.createBiquadFilter();
        this.subBassFilterNode.type = 'peaking';
        this.subBassFilterNode.frequency.value = 40; // Hz - Deep sub-bass
        this.subBassFilterNode.Q.value = 2.0; // Narrow band for precision

        // Mid-bass filter (60-150Hz) - Adds punch and warmth
        this.midBassFilterNode = this.audioCtx.createBiquadFilter();
        this.midBassFilterNode.type = 'peaking';
        this.midBassFilterNode.frequency.value = 100; // Hz - Mid-bass punch
        this.midBassFilterNode.Q.value = 1.5; // Moderate Q for natural sound

        // Upper bass filter (150-300Hz) - Clarity and definition
        this.bassFilterNode = this.audioCtx.createBiquadFilter();
        this.bassFilterNode.type = 'peaking';
        this.bassFilterNode.frequency.value = 200; // Hz - Upper bass clarity
        this.bassFilterNode.Q.value = 1.0; // Gentle slope

        // Bass-specific compressor for dynamic enhancement
        this.bassCompressorNode = this.audioCtx.createDynamicsCompressor();
        this.bassCompressorNode.threshold.value = -25;
        this.bassCompressorNode.knee.value = 10;
        this.bassCompressorNode.attack.value = 0.001;
        this.bassCompressorNode.release.value = 0.1;

        // === CLARITY ENHANCEMENT SYSTEM ===
        // Presence filter for vocal clarity
        this.presenceFilterNode = this.audioCtx.createBiquadFilter();
        this.presenceFilterNode.type = 'peaking';
        this.presenceFilterNode.frequency.value = 2800; // Hz - Vocal presence
        this.presenceFilterNode.Q.value = 1.2; // Focused enhancement

        // Treble filter for sparkle and air
        this.trebleFilterNode = this.audioCtx.createBiquadFilter();
        this.trebleFilterNode.type = 'highshelf';
        this.trebleFilterNode.frequency.value = 8000; // Hz - Treble sparkle
        this.trebleFilterNode.Q.value = 0.7; // Smooth response

        // High-frequency clarity filter
        this.clarityFilterNode = this.audioCtx.createBiquadFilter();
        this.clarityFilterNode.type = 'peaking';
        this.clarityFilterNode.frequency.value = 12000; // Hz - Air and clarity
        this.clarityFilterNode.Q.value = 0.8; // Gentle enhancement

        // === REVERB SYSTEM ===
        this.reverbNode = this.audioCtx.createConvolver();
        this.reverbGainNode = this.audioCtx.createGain();
        this.dryGainNode = this.audioCtx.createGain();

        // Create enhanced reverb buffer
        const reverbBuffer = this.createReverbBuffer();
        this.reverbNode.buffer = reverbBuffer;

        // === NEW HARMONIC ENHANCEMENT ENGINE ===
        this.harmonicNode = this.audioCtx.createWaveShaper();
        const harmonicCurve = new Float32Array(44100);
        for (let i = 0; i < 44100; i++) {
          const x = (i * 2) / 44100 - 1;
          harmonicCurve[i] = (3 + 2) * x * (1 - Math.abs(x)) / 3;
        }
        this.harmonicNode.curve = harmonicCurve;

        // === DYNAMIC RANGE EXPANSION ===
        this.dynamicNode = this.audioCtx.createDynamicsCompressor();
        this.dynamicNode.threshold.value = -50;
        this.dynamicNode.knee.value = 5;
        this.dynamicNode.ratio.value = 0.8;  // Ratio < 1 for expansion

        // === PSYCHOACOUSTIC BASS ENGINE ===
        this.psychoBassNode = this.audioCtx.createBiquadFilter();
        this.psychoBassNode.type = 'peaking';
        this.psychoBassNode.frequency.value = 55;
        this.psychoBassNode.Q.value = 2.5;

        // === ENHANCED STEREO DEPTH ===
        this.stereoDepthNode = this.audioCtx.createStereoPanner();

        // Apply initial settings
        await this.updateAudioNodes();

        this.isInitialized = true;
        console.log('🎵 Enhanced Audio System Initialized - Multi-band Bass + Balanced Stereo + Crystal Clarity');
      } catch (error) {
        console.error('Error initializing enhanced audio nodes:', error);
        throw error;
      }
    }

    // Enhanced reverb buffer for spatial audio
    createReverbBuffer() {
      const sampleRate = this.audioCtx.sampleRate;
      const length = sampleRate * 1.2; // 1.2 second reverb for more spaciousness
      const buffer = this.audioCtx.createBuffer(2, length, sampleRate);

      for (let channel = 0; channel < 2; channel++) {
        const channelData = buffer.getChannelData(channel);
        for (let i = 0; i < length; i++) {
          // Enhanced decay curve for more natural reverb
          const decay = Math.exp(-i / (sampleRate * 0.6));
          const earlyReflection = Math.exp(-i / (sampleRate * 0.1)) * 0.3;

          // Add some stereo variation
          const stereoVariation = channel === 0 ? 1.0 : 0.95;

          channelData[i] = (Math.random() * 2 - 1) * (decay + earlyReflection) * 0.25 * stereoVariation;
        }
      }

      return buffer;
    }

    // Update audio node values
    async updateAudioNodes() {
      if (!this.audioCtx || !this.delayNode || !this.gainNode || !this.compressorNode) {
        console.warn('Audio nodes not initialized');
        return;
      }

      try {
        // Helper function untuk safe setValue dengan validasi
        const safeSetValue = (param, value, paramName, min = -Infinity, max = Infinity) => {
          if (!param || typeof value !== 'number' || !isFinite(value)) {
            console.warn(`Invalid ${paramName} value:`, value);
            return false;
          }

          const clampedValue = Math.max(min, Math.min(max, value));
          if (clampedValue !== value) {
            console.warn(`${paramName} clamped from ${value} to ${clampedValue}`);
          }

          try {
            param.setValueAtTime(clampedValue, this.audioCtx.currentTime);
            return true;
          } catch (error) {
            console.error(`Error setting ${paramName}:`, error);
            return false;
          }
        };

        // Update delay settings dengan validasi
        safeSetValue(this.delayNode.delayTime, this.settings.delayTime || DEFAULT_DELAY, 'delayTime', 0.001, 0.1);

        // Update gain settings dengan validasi
        safeSetValue(this.gainNode.gain, this.settings.gainValue || DEFAULT_GAIN, 'gain', 0.1, 3.0);

        // Update compressor settings dengan validasi
        safeSetValue(this.compressorNode.threshold, this.settings.threshold || DEFAULT_THRESHOLD, 'threshold', -100, 0);
        safeSetValue(this.compressorNode.ratio, this.settings.ratio || DEFAULT_RATIO, 'ratio', 1, 20);
        safeSetValue(this.compressorNode.attack, this.settings.attack || DEFAULT_ATTACK, 'attack', 0.001, 1);
        safeSetValue(this.compressorNode.release, this.settings.release || DEFAULT_RELEASE, 'release', 0.01, 1);

        // Update reverb settings dengan validasi
        if (this.reverbGainNode && this.dryGainNode) {
          const reverbWet = this.settings.reverbWet || DEFAULT_REVERB_WET;
          const dryLevel = 1.0 - reverbWet;

          safeSetValue(this.reverbGainNode.gain, reverbWet, 'reverbWet', 0, 1);
          safeSetValue(this.dryGainNode.gain, dryLevel, 'dryLevel', 0, 1);
        }

        // === ENHANCED MULTI-BAND BASS SYSTEM ===
        if (this.subBassFilterNode) {
          const subBassBoost = this.settings.subBassBoost || DEFAULT_SUB_BASS_BOOST;
          const subBassGain = (subBassBoost - 1) * 12; // More aggressive for sub-bass
          safeSetValue(this.subBassFilterNode.gain, subBassGain, 'subBassGain', -20, 24);
        }

        if (this.midBassFilterNode) {
          const midBassBoost = this.settings.midBassBoost || DEFAULT_MID_BASS_BOOST;
          const midBassGain = (midBassBoost - 1) * 10; // Strong mid-bass enhancement
          safeSetValue(this.midBassFilterNode.gain, midBassGain, 'midBassGain', -20, 20);
        }

        if (this.bassFilterNode) {
          const bassBoost = this.settings.bassBoost || DEFAULT_BASS_BOOST;
          const bassGain = (bassBoost - 1) * 8; // Upper bass clarity
          safeSetValue(this.bassFilterNode.gain, bassGain, 'bassGain', -20, 20);
        }

        // Bass compressor for dynamic enhancement
        if (this.bassCompressorNode) {
          const bassRatio = this.settings.bassCompressorRatio || DEFAULT_BASS_COMPRESSOR_RATIO;
          safeSetValue(this.bassCompressorNode.ratio, bassRatio, 'bassCompressorRatio', 1, 10);
        }

        // === CLARITY ENHANCEMENT SYSTEM ===
        if (this.presenceFilterNode) {
          const presenceBoost = this.settings.presenceBoost || DEFAULT_PRESENCE_BOOST;
          const presenceGain = (presenceBoost - 1) * 6; // Vocal clarity
          safeSetValue(this.presenceFilterNode.gain, presenceGain, 'presenceGain', -20, 20);
        }

        if (this.trebleFilterNode) {
          const trebleBoost = this.settings.trebleBoost || DEFAULT_TREBLE_BOOST;
          const trebleGain = (trebleBoost - 1) * 8; // Treble sparkle
          safeSetValue(this.trebleFilterNode.gain, trebleGain, 'trebleGain', -20, 20);
        }

        if (this.clarityFilterNode) {
          // Auto-adjust clarity based on treble setting
          const clarityGain = (this.settings.trebleBoost - 1) * 4; // Subtle high-freq clarity
          safeSetValue(this.clarityFilterNode.gain, clarityGain, 'clarityGain', -10, 15);
        }

        // === BALANCED STEREO ENHANCEMENT ===
        if (this.leftGainNode && this.rightGainNode) {
          const stereoBalance = this.settings.stereoBalance || DEFAULT_STEREO_BALANCE;

          // Fixed stereo balance calculation
          // stereoBalance: -1.0 = full left, 0.0 = center, 1.0 = full right
          const leftGain = stereoBalance <= 0 ? 1.0 : 1.0 - stereoBalance;   // Full when balance is left/center, reduced when right
          const rightGain = stereoBalance >= 0 ? 1.0 : 1.0 + stereoBalance;  // Full when balance is right/center, reduced when left

          safeSetValue(this.leftGainNode.gain, leftGain, 'leftGain', 0, 1);
          safeSetValue(this.rightGainNode.gain, rightGain, 'rightGain', 0, 1);
        }

        // Minimal stereo width processing (only if width > 1.0)
        if (this.leftDelayNode && this.rightDelayNode) {
          const stereoWidth = this.settings.stereoWidth || DEFAULT_STEREO_WIDTH;
          if (stereoWidth > 1.0) {
            const widthDelay = (stereoWidth - 1.0) * 0.003; // Reduced delay for subtle effect
            safeSetValue(this.leftDelayNode.delayTime, widthDelay, 'leftDelay', 0, 0.02);
            safeSetValue(this.rightDelayNode.delayTime, widthDelay, 'rightDelay', 0, 0.02);
          } else {
            // No delay for balanced stereo
            safeSetValue(this.leftDelayNode.delayTime, 0, 'leftDelay', 0, 0.02);
            safeSetValue(this.rightDelayNode.delayTime, 0, 'rightDelay', 0, 0.02);
          }
        }

        // Update new engine parameters
        if (this.harmonicNode) {
          const harmonicIntensity = this.settings.harmonicIntensity || DEFAULT_HARMONIC_INTENSITY;
          this.harmonicNode.curve = this.createHarmonicCurve(harmonicIntensity);
        }

        if (this.dynamicNode) {
          const dynamicRange = this.settings.dynamicRange || DEFAULT_DYNAMIC_RANGE;
          this.dynamicNode.ratio.value = 1 / dynamicRange;  // Inverse ratio for expansion
        }

        if (this.psychoBassNode) {
          const psychoBass = this.settings.psychoBass || DEFAULT_PSYCHO_BASS;
          this.psychoBassNode.gain.value = psychoBass * 12;
        }

        if (this.stereoDepthNode) {
          const stereoDepth = this.settings.stereoDepth || DEFAULT_STEREO_DEPTH;
          this.stereoDepthNode.pan.value = 0;  // Center
          // Apply modulation for enhanced depth
          this.modulateStereoDepth(stereoDepth);
        }

        console.log('Audio settings updated successfully');
      } catch (error) {
        console.error('Error updating audio nodes:', error);
      }
    }

    // Removed updateSpatialAudioNodes method - functionality integrated into updateAudioNodes

    // Connect audio nodes to video element
    async connectAudio(videoElement) {
      try {
        // Always cleanup and recreate audio context for new video
        await this.cleanup();
        
        if (!this.audioCtx || this.audioCtx.state === 'closed') {
          const AudioContextClass = window.AudioContext || window['webkitAudioContext'];
          this.audioCtx = new AudioContextClass();
        }

        // Create new source for this video
        this.source = this.audioCtx.createMediaElementSource(videoElement);
        this.currentVideo = videoElement;

        // Initialize audio nodes with new context
        await this.initializeAudioNodes();

        // Connect processing chain
        await this.connectProcessingChain();

        // Setup event listeners
        this.setupVideoEventListeners(videoElement);

        // Resume audio context if needed
        if (this.audioCtx.state === 'suspended') {
          await this.audioCtx.resume();
        }

      } catch (error) {
        console.error("Failed to connect audio:", error);
        // Fallback to direct output on error
        if (this.source) {
          this.source.connect(this.audioCtx.destination);
        }
      }
    }

    async connectProcessingChain() {
      try {
        // Core processing chain
        this.source
          .connect(this.gainNode)
          .connect(this.compressorNode);

        // Bass enhancement chain
        this.compressorNode
          .connect(this.subBassFilterNode)
          .connect(this.midBassFilterNode)
          .connect(this.bassFilterNode)
          .connect(this.bassCompressorNode);

        // Clarity chain
        this.bassCompressorNode
          .connect(this.presenceFilterNode)
          .connect(this.trebleFilterNode)
          .connect(this.clarityFilterNode);

        // Split for stereo processing
        this.clarityFilterNode.connect(this.splitterNode);

        // Process each channel separately
        this.splitterNode.connect(this.leftGainNode, 0);
        this.splitterNode.connect(this.rightGainNode, 1);

        this.leftGainNode.connect(this.leftDelayNode);
        this.rightGainNode.connect(this.rightDelayNode);

        // Mix processed channels
        this.leftDelayNode.connect(this.mergerNode, 0, 0);  // Left -> Left
        this.rightDelayNode.connect(this.mergerNode, 0, 1); // Right -> Right

        // Final effects chain
        this.mergerNode
          .connect(this.harmonicNode)
          .connect(this.dynamicNode)
          .connect(this.psychoBassNode)
          .connect(this.stereoDepthNode)
          .connect(this.audioCtx.destination);

        console.log('🎵 Audio processing chain connected successfully');
      } catch (error) {
        console.error('Failed to connect processing chain:', error);
        // Simple fallback chain on error
        this.source
          .connect(this.gainNode)
          .connect(this.compressorNode)
          .connect(this.audioCtx.destination);
      }
    }

    // Helper method to reset audio context
    async resetAudioContext() {
      if (this.audioCtx && this.audioCtx.state !== 'closed') {
        await this.audioCtx.close();
      }
      this.audioCtx = new (window.AudioContext || window['webkitAudioContext'])();
    }

    // Setup event listeners for video element
    setupVideoEventListeners(videoElement) {
      // Clean up existing listeners
      this.removeVideoEventListeners();

      const listeners = {
        'ended': () => this.cleanup(),
        'pause': () => this.handleVideoPause(),
        'play': () => this.handleVideoPlay(),
        'loadstart': () => this.handleVideoChange()
      };

      Object.entries(listeners).forEach(([event, handler]) => {
        videoElement.addEventListener(event, handler);
        this.eventListeners.set(`${event}_${videoElement}`, { element: videoElement, event, handler });
      });
    }

    // Remove video event listeners with error handling
    removeVideoEventListeners() {
      try {
        this.eventListeners.forEach(({ element, event, handler }) => {
          if (element && typeof element.removeEventListener === 'function') {
            element.removeEventListener(event, handler);
          }
        });
        this.eventListeners.clear();
      } catch (error) {
        console.error('Error removing video event listeners:', error);
        // Force clear the map even if removal fails
        this.eventListeners.clear();
      }
    }

    // Handle video pause
    handleVideoPause() {
      if (this.audioCtx && this.audioCtx.state === 'running') {
        this.audioCtx.suspend().catch(console.error);
      }
    }

    // Handle video play
    async handleVideoPlay() {
      if (this.audioCtx && this.audioCtx.state === 'suspended') {
        try {
          await this.audioCtx.resume();
        } catch (error) {
          console.error("Failed to resume audio context:", error);
        }
      }
    }

    // Handle video change (new video loaded)
    async handleVideoChange() {
      try {
        const videoElement = await this.findVideoElement();
        if (videoElement !== this.currentVideo) {
          await this.connectAudio(videoElement);
        }
      } catch (error) {
        console.error("Failed to handle video change:", error);
      }
    }

    // Enhanced cleanup with better error handling
    async cleanup() {
      try {
        // Clear any pending timeouts
        if (this.updateTimeout) {
          clearTimeout(this.updateTimeout);
          this.updateTimeout = null;
        }

        // Remove event listeners first
        this.removeVideoEventListeners();

        // Disconnect audio source
        if (this.source) {
          try {
            this.source.disconnect();
          } catch (error) {
            console.warn('Error disconnecting audio source:', error);
          }
          this.source = null;
        }

        // Close audio context
        if (this.audioCtx && this.audioCtx.state !== 'closed') {
          try {
            await this.audioCtx.close();
          } catch (error) {
            console.warn('Error closing audio context:', error);
          }
        }

        // Clean up all audio nodes
        this.audioCtx = null;
        this.delayNode = null;
        this.gainNode = null;
        this.compressorNode = null;

        // Clean up bass enhancement nodes
        this.subBassFilterNode = null;
        this.midBassFilterNode = null;
        this.bassFilterNode = null;
        this.bassCompressorNode = null;

        // Clean up clarity enhancement nodes
        this.presenceFilterNode = null;
        this.trebleFilterNode = null;
        this.clarityFilterNode = null;

        // Clean up balanced stereo processing nodes
        this.splitterNode = null;
        this.mergerNode = null;
        this.leftGainNode = null;
        this.rightGainNode = null;
        this.leftDelayNode = null;
        this.rightDelayNode = null;

        // Clean up reverb nodes
        this.reverbNode = null;
        this.reverbGainNode = null;
        this.dryGainNode = null;

        // Clean up new audio enhancement engines
        this.harmonicNode = null;
        this.dynamicNode = null;
        this.psychoBassNode = null;
        this.stereoDepthNode = null;

        this.currentVideo = null;
        this.isInitialized = false;

        console.log('Audio processor cleanup completed successfully');
      } catch (error) {
        console.error("Cleanup error:", error);
      }
    }

    // Update settings with validation and non-blocking notifications
    updateSetting(key, value, validator, unit = '') {
      if (!validator(value)) {
        this.showNotification(`Nilai ${key} tidak valid`, 'error');
        return false;
      }

      this.settings[key] = value;
      GM_setValue(key, value);
      this.updateAudioNodes();
      this.showNotification(`${key} diatur ke ${value}${unit}`, 'success');
      return true;
    }

    // Save all current settings to persistent storage
    saveAllSettings() {
      try {
        Object.entries(this.settings).forEach(([key, value]) => {
          GM_setValue(key, value);
        });
        this.showNotification('🔄 Pengaturan tersimpan otomatis!', 'success');
        console.log('All settings saved to persistent storage');
      } catch (error) {
        console.error('Error saving settings:', error);
        this.showNotification('❌ Gagal menyimpan pengaturan', 'error');
      }
    }

    // Enhanced notification system with better styling
    showNotification(message, type = 'info') {
      // Remove existing notifications
      const existingNotifications = document.querySelectorAll('.audio-processor-notification');
      existingNotifications.forEach(notif => notif.remove());

      // Create notification element
      const notification = document.createElement('div');
      notification.className = 'audio-processor-notification';
      notification.innerHTML = `
        <div class="notification-icon">
          ${type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️'}
        </div>
        <div class="notification-message">${message}</div>
      `;

      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 16px 20px;
        border-radius: 12px;
        color: white;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-size: 14px;
        font-weight: 500;
        z-index: 10001;
        display: flex;
        align-items: center;
        gap: 12px;
        min-width: 280px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        animation: slideInRight 0.3s ease-out;
        background: ${type === 'error' ?
          'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)' :
          type === 'success' ?
          'linear-gradient(135deg, #4caf50 0%, #45a049 100%)' :
          'linear-gradient(135deg, #2196f3 0%, #1976d2 100%)'};
      `;

      // Add animation styles
      const style = document.createElement('style');
      style.textContent = `
        @keyframes slideInRight {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }

        @keyframes slideOutRight {
          from {
            transform: translateX(0);
            opacity: 1;
          }
          to {
            transform: translateX(100%);
            opacity: 0;
          }
        }

        .notification-icon {
          font-size: 18px;
        }

        .notification-message {
          flex: 1;
        }
      `;

      document.head.appendChild(style);
      document.body.appendChild(notification);

      // Auto remove after 4 seconds with animation
      setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease-in forwards';
        setTimeout(() => {
          if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
          }
          if (style.parentNode) {
            style.parentNode.removeChild(style);
          }
        }, 300);
      }, 4000);
    }

    // Add visual status indicator
    createStatusIndicator() {
      // Remove existing indicator
      const existing = document.getElementById('audio-processor-status');
      if (existing) existing.remove();

      const indicator = document.createElement('div');
      indicator.id = 'audio-processor-status';
      indicator.innerHTML = `
        <div class="status-icon">🎵</div>
        <div class="status-text">Audio Enhanced</div>
      `;

      indicator.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        padding: 12px 16px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 25px;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-size: 12px;
        font-weight: 600;
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: 8px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        cursor: pointer;
        transition: all 0.3s ease;
        opacity: 0.8;
      `;

      indicator.addEventListener('mouseenter', () => {
        indicator.style.opacity = '1';
        indicator.style.transform = 'scale(1.05)';
      });

      indicator.addEventListener('mouseleave', () => {
        indicator.style.opacity = '0.8';
        indicator.style.transform = 'scale(1)';
      });

      indicator.addEventListener('click', () => {
        this.showSpatialAudioDialog();
      });

      document.body.appendChild(indicator);

      // Auto-hide after 10 seconds
      setTimeout(() => {
        if (indicator.parentNode) {
          indicator.style.opacity = '0';
          setTimeout(() => {
            if (indicator.parentNode) {
              indicator.parentNode.removeChild(indicator);
            }
          }, 300);
        }
      }, 10000);
    }

    // === ENHANCED PRESET SYSTEM - Optimized for Deep Bass Clarity ===
    applyPreset(presetName) {
      const presets = {
        dolby: {
          delayTime: 0.010,
          gainValue: 1.4,
          threshold: -16,
          ratio: 6,
          attack: 0.002,
          release: 0.08,
          stereoWidth: 1.2,       // Balanced stereo with minimal widening
          stereoBalance: 0.0,     // Centered balance
          reverbWet: 0.22,
          // Enhanced Multi-Band Bass
          subBassBoost: 2.5,      // Deep sub-bass for impact
          midBassBoost: 1.8,      // Mid-bass punch
          bassBoost: 1.6,         // Upper bass clarity
          bassCompressorRatio: 4, // Dynamic bass enhancement
          // Crystal Clarity
          trebleBoost: 1.35,
          presenceBoost: 1.3
        },
        superStereo: {
          delayTime: 0.008,
          gainValue: 1.35,
          threshold: -18,
          ratio: 5,
          attack: 0.001,
          release: 0.06,
          stereoWidth: 1.0,       // Balanced stereo - no widening for clear vocals
          stereoBalance: 0.0,     // Centered balance
          reverbWet: 0.18,
          // Aggressive Bass Enhancement
          subBassBoost: 2.8,      // Maximum sub-bass
          midBassBoost: 2.0,      // Strong mid-bass
          bassBoost: 1.7,         // Clear upper bass
          bassCompressorRatio: 5, // Strong bass compression
          // Bright Clarity
          trebleBoost: 1.4,
          presenceBoost: 1.35
        },
        headphone: {
          delayTime: 0.012,
          gainValue: 1.3,
          threshold: -20,
          ratio: 4,
          attack: 0.003,
          release: 0.1,
          stereoWidth: 1.0,       // Balanced stereo for headphones
          stereoBalance: 0.0,     // Centered balance
          reverbWet: 0.2,
          // Balanced Bass for Headphones
          subBassBoost: 2.2,      // Controlled sub-bass
          midBassBoost: 1.7,      // Warm mid-bass
          bassBoost: 1.5,         // Clean upper bass
          bassCompressorRatio: 3, // Gentle bass compression
          // Smooth Clarity
          trebleBoost: 1.25,
          presenceBoost: 1.2
        },
        concertHall: {
          delayTime: 0.015,
          gainValue: 1.25,
          threshold: -14,
          ratio: 7,
          attack: 0.004,
          release: 0.12,
          stereoWidth: 1.1,       // Minimal width for spacious feel
          stereoBalance: 0.0,     // Centered balance
          reverbWet: 0.35,        // Rich reverb
          // Natural Bass Response
          subBassBoost: 2.0,      // Natural sub-bass
          midBassBoost: 1.6,      // Balanced mid-bass
          bassBoost: 1.4,         // Subtle upper bass
          bassCompressorRatio: 2, // Light bass compression
          // Natural Clarity
          trebleBoost: 1.2,
          presenceBoost: 1.15
        },
        bassBoost: {              // New: Dedicated bass preset
          delayTime: 0.008,
          gainValue: 1.5,
          threshold: -22,
          ratio: 3,
          attack: 0.001,
          release: 0.05,
          stereoWidth: 1.0,       // Balanced stereo for bass focus
          stereoBalance: 0.0,     // Centered balance
          reverbWet: 0.15,
          // Maximum Bass Enhancement
          subBassBoost: 3.2,      // Extreme sub-bass
          midBassBoost: 2.5,      // Powerful mid-bass
          bassBoost: 2.0,         // Strong upper bass
          bassCompressorRatio: 6, // Aggressive bass compression
          // Balanced Clarity
          trebleBoost: 1.1,
          presenceBoost: 1.0
        }
      };

      const preset = presets[presetName];
      if (!preset) {
        this.showNotification(`Preset ${presetName} tidak ditemukan`, 'error');
        return;
      }

      // Apply preset settings dengan validasi
      Object.entries(preset).forEach(([key, value]) => {
        // Validasi nilai sebelum diterapkan
        if (typeof value === 'number' && isFinite(value)) {
          this.settings[key] = value;
          GM_setValue(key, value);
        } else {
          console.warn(`Invalid preset value for ${key}:`, value);
        }
      });

      this.updateAudioNodes();
      this.saveAllSettings(); // Auto-save settings
      this.showNotification(`Preset ${presetName} diterapkan dan tersimpan`, 'success');
    }

    // === MODERN UI DIALOGS ===
    createModernDialog(title, content) {
      // Remove existing dialog if any
      const existingDialog = document.getElementById('audio-processor-dialog');
      if (existingDialog) {
        existingDialog.remove();
      }

      const dialog = document.createElement('div');
      dialog.id = 'audio-processor-dialog';
      dialog.innerHTML = `
        <div class="dialog-overlay">
          <div class="dialog-container">
            <div class="dialog-header">
              <h3>${title}</h3>
              <button class="dialog-close">×</button>
            </div>
            <div class="dialog-content">
              ${content}
            </div>
          </div>
        </div>
      `;

      // Add modern styling
      const style = document.createElement('style');
      style.textContent = `
        #audio-processor-dialog {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 10000;
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .dialog-overlay {
          background: rgba(0, 0, 0, 0.7);
          backdrop-filter: blur(5px);
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          animation: fadeIn 0.3s ease;
        }

        .dialog-container {
          background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%);
          border-radius: 12px;
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
          max-width: 500px;
          width: 90%;
          max-height: 80vh;
          overflow: hidden;
          animation: slideIn 0.3s ease;
        }

        .dialog-header {
          background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
          color: white;
          padding: 20px;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .dialog-header h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
        }

        .dialog-close {
          background: none;
          border: none;
          color: white;
          font-size: 24px;
          cursor: pointer;
          padding: 0;
          width: 30px;
          height: 30px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: background 0.2s;
        }

        .dialog-close:hover {
          background: rgba(255, 255, 255, 0.2);
        }

        .dialog-content {
          padding: 20px;
          color: #e0e0e0;
          max-height: 60vh;
          overflow-y: auto;
        }

        .setting-group {
          margin-bottom: 20px;
          padding: 15px;
          background: rgba(255, 255, 255, 0.05);
          border-radius: 8px;
          border-left: 4px solid #ff6b6b;
        }

        .setting-group h4 {
          margin: 0 0 15px 0;
          color: #ff6b6b;
          font-size: 16px;
        }

        .setting-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
        }

        .setting-item:last-child {
          margin-bottom: 0;
        }

        .setting-label {
          flex: 1;
          margin-right: 15px;
          font-size: 14px;
        }

        .setting-control {
          display: flex;
          align-items: center;
          gap: 10px;
        }

        .setting-slider {
          width: 120px;
          height: 6px;
          border-radius: 3px;
          background: #444;
          outline: none;
          -webkit-appearance: none;
        }

        .setting-slider::-webkit-slider-thumb {
          -webkit-appearance: none;
          width: 18px;
          height: 18px;
          border-radius: 50%;
          background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
          cursor: pointer;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .setting-value {
          min-width: 50px;
          text-align: center;
          font-size: 12px;
          color: #ff6b6b;
          font-weight: 600;
        }

        .preset-buttons {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
          gap: 10px;
          margin-top: 20px;
        }

        .preset-btn {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border: none;
          padding: 12px 16px;
          border-radius: 8px;
          cursor: pointer;
          font-size: 14px;
          font-weight: 500;
          transition: all 0.2s;
        }

        .preset-btn:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        @keyframes slideIn {
          from { transform: translateY(-50px) scale(0.9); opacity: 0; }
          to { transform: translateY(0) scale(1); opacity: 1; }
        }
      `;

      document.head.appendChild(style);
      document.body.appendChild(dialog);

      // Enhanced event listeners dengan cleanup
      const closeDialog = () => {
        // Cleanup slider listeners
        const sliders = dialog.querySelectorAll('.setting-slider');
        sliders.forEach(slider => {
          if (slider._handleInput) {
            slider.removeEventListener('input', slider._handleInput);
            delete slider._handleInput;
          }
        });

        // Remove dialog and style
        dialog.remove();
        style.remove();
      };

      dialog.querySelector('.dialog-close').onclick = closeDialog;

      dialog.querySelector('.dialog-overlay').onclick = (e) => {
        if (e.target === e.currentTarget) {
          closeDialog();
        }
      };

      // Close on Escape key
      const handleKeydown = (e) => {
        if (e.key === 'Escape') {
          closeDialog();
          document.removeEventListener('keydown', handleKeydown);
        }
      };
      document.addEventListener('keydown', handleKeydown);

      return dialog;
    }

    // === DIALOG METHODS ===
    showSpatialAudioDialog() {
      const content = `
        <div class="setting-group">
          <h4>🎭 Balanced Stereo Control</h4>
          <div class="setting-item">
            <span class="setting-label">Stereo Balance</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="stereoBalance" min="-1" max="1" step="0.1" value="${this.settings.stereoBalance}">
              <span class="setting-value" id="stereoBalanceValue">${this.settings.stereoBalance > 0 ? 'R' + this.settings.stereoBalance : this.settings.stereoBalance < 0 ? 'L' + Math.abs(this.settings.stereoBalance) : 'Center'}</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Stereo Width</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="stereoWidth" min="1" max="1.5" step="0.1" value="${this.settings.stereoWidth}">
              <span class="setting-value" id="stereoWidthValue">${this.settings.stereoWidth}x</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Reverb Amount</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="reverbWet" min="0" max="0.5" step="0.05" value="${this.settings.reverbWet}">
              <span class="setting-value" id="reverbWetValue">${this.settings.reverbWet}</span>
            </div>
          </div>
        </div>

        <div class="setting-group">
          <h4>🔊 Multi-Band Bass Enhancement</h4>
          <div class="setting-item">
            <span class="setting-label">Sub-Bass (20-60Hz)</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="subBassBoost" min="1.0" max="3.5" step="0.1" value="${this.settings.subBassBoost}">
              <span class="setting-value" id="subBassBoostValue">${this.settings.subBassBoost}x</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Mid-Bass (60-150Hz)</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="midBassBoost" min="1.0" max="3.0" step="0.1" value="${this.settings.midBassBoost}">
              <span class="setting-value" id="midBassBoostValue">${this.settings.midBassBoost}x</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Upper Bass (150-300Hz)</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="bassBoost" min="1.0" max="2.5" step="0.1" value="${this.settings.bassBoost}">
              <span class="setting-value" id="bassBoostValue">${this.settings.bassBoost}x</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Bass Compressor</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="bassCompressorRatio" min="1" max="8" step="0.5" value="${this.settings.bassCompressorRatio}">
              <span class="setting-value" id="bassCompressorRatioValue">${this.settings.bassCompressorRatio}:1</span>
            </div>
          </div>
        </div>

        <div class="setting-group">
          <h4>✨ Clarity Enhancement</h4>
          <div class="setting-item">
            <span class="setting-label">Presence (Vocals)</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="presenceBoost" min="0.8" max="2.5" step="0.1" value="${this.settings.presenceBoost}">
              <span class="setting-value" id="presenceBoostValue">${this.settings.presenceBoost}x</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Treble (Sparkle)</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="trebleBoost" min="0.8" max="2.5" step="0.1" value="${this.settings.trebleBoost}">
              <span class="setting-value" id="trebleBoostValue">${this.settings.trebleBoost}x</span>
            </div>
          </div>
        </div>

        <div class="preset-buttons">
          <button class="preset-btn" onclick="audioProcessor.applyPreset('dolby')">🎭 Dolby Balanced</button>
          <button class="preset-btn" onclick="audioProcessor.applyPreset('superStereo')">🎪 Clear Vocals</button>
          <button class="preset-btn" onclick="audioProcessor.applyPreset('bassBoost')">🔊 Bass Boost</button>
          <button class="preset-btn" onclick="audioProcessor.applyPreset('headphone')">🎧 Headphone</button>
          <button class="preset-btn" onclick="audioProcessor.applyPreset('concertHall')">🎵 Concert Hall</button>
        </div>
      `;

      const dialog = this.createModernDialog('🎛️ Audio Enhancement', content);
      this.setupSliderListeners(dialog);
      this.initializeSliderValues(dialog);
    }

    showBasicAudioDialog() {
      const content = `
        <div class="setting-group">
          <h4>🔊 Basic Controls</h4>
          <div class="setting-item">
            <span class="setting-label">Audio Gain</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="gainValue" min="0.8" max="2" step="0.1" value="${this.settings.gainValue}">
              <span class="setting-value" id="gainValueValue">${this.settings.gainValue}x</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Audio Delay</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="delayTime" min="0.005" max="0.05" step="0.005" value="${this.settings.delayTime}">
              <span class="setting-value" id="delayTimeValue">${this.settings.delayTime}s</span>
            </div>
          </div>
        </div>
      `;

      const dialog = this.createModernDialog('🔧 Basic Audio Settings', content);
      this.setupSliderListeners(dialog);
      this.initializeSliderValues(dialog);
    }

    showAdvancedDialog() {
      const content = `
        <div class="setting-group">
          <h4>🎛️ Compressor Settings</h4>
          <div class="setting-item">
            <span class="setting-label">Threshold</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="threshold" min="-30" max="-10" step="1" value="${this.settings.threshold}">
              <span class="setting-value" id="thresholdValue">${this.settings.threshold}dB</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Ratio</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="ratio" min="2" max="8" step="0.5" value="${this.settings.ratio}">
              <span class="setting-value" id="ratioValue">${this.settings.ratio}:1</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Attack</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="attack" min="0.001" max="0.01" step="0.001" value="${this.settings.attack}">
              <span class="setting-value" id="attackValue">${this.settings.attack}s</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Release</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="release" min="0.05" max="0.2" step="0.01" value="${this.settings.release}">
              <span class="setting-value" id="releaseValue">${this.settings.release}s</span>
            </div>
          </div>
        </div>
      `;

      const dialog = this.createModernDialog('⚙️ Compressor Settings', content);
      this.setupSliderListeners(dialog);
      this.initializeSliderValues(dialog);
    }

    showCurrentSettings() {
      const content = `
        <div class="setting-group">
          <h4>📊 Current Audio Settings</h4>
          <div style="font-family: monospace; font-size: 12px; line-height: 1.6;">
            <strong>🎛️ Basic:</strong><br>
            • Delay: ${this.settings.delayTime}s<br>
            • Gain: ${this.settings.gainValue}x<br><br>

            <strong>🎚️ Compressor:</strong><br>
            • Threshold: ${this.settings.threshold}dB<br>
            • Ratio: ${this.settings.ratio}:1<br>
            • Attack: ${this.settings.attack}s<br>
            • Release: ${this.settings.release}s<br><br>

            <strong>🎭 Spatial:</strong><br>
            • Stereo Width: ${this.settings.stereoWidth}x<br>
            • Reverb: ${this.settings.reverbWet}<br><br>

            <strong>🎵 EQ:</strong><br>
            • Bass: ${this.settings.bassBoost}x<br>
            • Presence: ${this.settings.presenceBoost}x<br>
            • Treble: ${this.settings.trebleBoost}x<br>
          </div>
        </div>
      `;

      this.createModernDialog('📋 Audio Status', content);
    }

    setupSliderListeners(dialog) {
      const sliders = dialog.querySelectorAll('.setting-slider');

      sliders.forEach(slider => {
        const valueDisplay = dialog.querySelector(`#${slider.id}Value`);

        if (!valueDisplay) {
          console.warn(`Value display not found for slider: ${slider.id}`);
          return;
        }

        // Remove existing listeners to prevent duplicates
        if (slider._handleInput) {
          slider.removeEventListener('input', slider._handleInput);
        }

        // Create bound handler function
        const handleInput = (e) => {
          try {
            const value = parseFloat(e.target.value);
            const key = e.target.id;

            // Validate value
            if (isNaN(value)) {
              console.warn(`Invalid value for ${key}: ${e.target.value}`);
              return;
            }

            // Update display with proper formatting
            let displayValue = this.formatSliderValue(key, value);
            valueDisplay.textContent = displayValue;

            // Update setting and save
            this.settings[key] = value;
            GM_setValue(key, value);

            // Update audio nodes with debouncing
            clearTimeout(this.updateTimeout);
            this.updateTimeout = setTimeout(() => {
              this.updateAudioNodes();
            }, 100);

          } catch (error) {
            console.error(`Error handling slider input for ${e.target.id}:`, error);
          }
        };

        // Add event listener
        slider.addEventListener('input', handleInput);

        // Store reference for cleanup
        slider._handleInput = handleInput;
      });
    }

    // Helper method untuk format nilai slider
    formatSliderValue(key, value) {
      switch (key) {
        case 'delayTime':
        case 'attack':
        case 'release':
          return value.toFixed(3) + 's';
        case 'threshold':
          return value + 'dB';
        case 'ratio':
        case 'bassCompressorRatio':
          return value + ':1';
        case 'gainValue':
        case 'bassBoost':
        case 'subBassBoost':
        case 'midBassBoost':
        case 'trebleBoost':
        case 'presenceBoost':
        case 'stereoWidth':
          return value.toFixed(1) + 'x';
        case 'reverbWet':
          return value.toFixed(2);
        case 'stereoBalance':
          if (value > 0) return 'R' + value.toFixed(1);
          if (value < 0) return 'L' + Math.abs(value).toFixed(1);
          return 'Center';
        default:
          return value.toString();
      }
    }

    // Initialize slider values dan displays saat dialog dibuka
    initializeSliderValues(dialog) {
      const sliders = dialog.querySelectorAll('.setting-slider');

      sliders.forEach(slider => {
        const key = slider.id;
        const valueDisplay = dialog.querySelector(`#${key}Value`);

        if (valueDisplay && this.settings[key] !== undefined) {
          // Set slider value dari settings
          slider.value = this.settings[key];

          // Update display value
          const displayValue = this.formatSliderValue(key, this.settings[key]);
          valueDisplay.textContent = displayValue;

          // Trigger visual update
          slider.dispatchEvent(new Event('input', { bubbles: true }));
        }
      });
    }

    // Test method untuk memverifikasi semua slider berfungsi
    testSliders() {
      console.log('🧪 Testing Slider Functionality:');

      const testValues = {
        delayTime: 0.02,
        gainValue: 1.5,
        threshold: -18,
        ratio: 5,
        attack: 0.005,
        release: 0.1,
        stereoWidth: 2.0,
        reverbWet: 0.25,
        bassBoost: 1.4,
        trebleBoost: 1.3,
        presenceBoost: 1.2
      };

      Object.entries(testValues).forEach(([key, value]) => {
        const oldValue = this.settings[key];
        this.settings[key] = value;
        console.log(`✅ ${key}: ${oldValue} → ${value} (${this.formatSliderValue(key, value)})`);
      });

      this.updateAudioNodes();
      this.showNotification('Slider test completed - check console', 'info');
    }

    // Helper method for harmonic enhancement
    createHarmonicCurve(intensity) {
      const samples = 44100;
      const curve = new Float32Array(samples);
      for (let i = 0; i < samples; i++) {
        const x = (i * 2) / samples - 1;
        // Enhanced waveshaping with controlled harmonics
        curve[i] = Math.tanh(x * intensity) + 
                   0.5 * Math.tanh(x * 2 * intensity) +
                   0.25 * Math.tanh(x * 4 * intensity);
      }
      return curve;
    }

    // Method for stereo depth modulation
    modulateStereoDepth(depth) {
      const oscillator = this.audioCtx.createOscillator();
      const depthGain = this.audioCtx.createGain();
      
      oscillator.frequency.value = 0.1; // Slow modulation
      depthGain.gain.value = depth;
      
      oscillator.connect(depthGain);
      depthGain.connect(this.stereoDepthNode.pan);
      
      oscillator.start();
      setTimeout(() => oscillator.stop(), 50); // Short modulation
    }
  }

  // Create global audio processor instance
  const audioProcessor = new AudioProcessor();

  // === STREAMLINED MENU - Essential Controls Only ===
  
  // Enhanced Audio Processing
  GM_registerMenuCommand("✨ Master Audio Controls", () => {
    audioProcessor.showSpatialAudioDialog();
  });

  GM_registerMenuCommand("🎛️ Advanced Audio Engine", () => {
    audioProcessor.showAdvancedEngineDialog();  // New dialog for harmonics/psychoacoustics
  });

  // Separator
  GM_registerMenuCommand("─────────────────", () => {});

  // Enhanced Presets System
  GM_registerMenuCommand("🎭 Dolby Balanced", () => {
    audioProcessor.applyPreset('dolby');
  });

  GM_registerMenuCommand("🎪 Crystal Clear", () => {
    audioProcessor.applyPreset('superStereo');
  });

  GM_registerMenuCommand("💫 Dynamic Bass", () => {  // Renamed for clarity
    audioProcessor.applyPreset('bassBoost');
  });

  GM_registerMenuCommand("🎧 Studio Monitor", () => {  // Updated name
    audioProcessor.applyPreset('headphone');
  });

  GM_registerMenuCommand("🏛️ Concert Hall", () => {
    audioProcessor.applyPreset('concertHall');
  });

  // Separator
  GM_registerMenuCommand("─────────────────", () => {});

  // Settings & Tools
  GM_registerMenuCommand("⚙️ Basic Settings", () => {
    audioProcessor.showBasicAudioDialog();
  });

  GM_registerMenuCommand("🔧 Compressor & Dynamics", () => {
    audioProcessor.showAdvancedDialog();
  });

  GM_registerMenuCommand("🎚️ Fine Tuning", () => {
    audioProcessor.showFineControlsDialog();  // New dialog for detailed adjustments
  });

  // Separator
  GM_registerMenuCommand("─────────────────", () => {});

  // Utility Commands
  GM_registerMenuCommand("📊 Status & Info", () => {
    audioProcessor.showCurrentSettings();
  });

  GM_registerMenuCommand("💾 Save Settings", () => {
    audioProcessor.saveAllSettings();
    audioProcessor.showNotification('Audio settings saved successfully', 'success');
  });

  GM_registerMenuCommand("🔄 Reset All", () => {
    if (confirm("Reset all audio settings to default?")) {
      audioProcessor.resetToDefaults();
      audioProcessor.showNotification('All settings reset to default values', 'info');
    }
  });

  // Optimized initialization with better error handling
  async function initializeScript() {
    try {
      await audioProcessor.init();

      // Setup navigation change detection for SPA behavior
      let lastUrl = location.href;
      const observer = new MutationObserver(() => {
        if (location.href !== lastUrl) {
          lastUrl = location.href;
          // Reinitialize on navigation change with delay
          setTimeout(async () => {
            try {
              await audioProcessor.init();
            } catch (error) {
              console.error("Failed to reinitialize on navigation:", error);
            }
          }, 1000);
        }
      });

      observer.observe(document.body, { childList: true, subtree: true });

      // Enhanced cleanup on page unload
      const cleanup = () => {
        try {
          observer.disconnect();
          audioProcessor.cleanup();
        } catch (error) {
          console.error("Error during cleanup:", error);
        }
      };

      window.addEventListener('beforeunload', cleanup);
      window.addEventListener('pagehide', cleanup);

    } catch (error) {
      console.error("Failed to initialize YouTube Audio Processor:", error);
      // Show user-friendly error notification
      if (typeof audioProcessor.showNotification === 'function') {
        audioProcessor.showNotification('❌ Audio enhancement failed to initialize', 'error');
      }
    }
  }

  // Initialize when DOM is ready
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", initializeScript);
  } else {
    initializeScript();
  }
})();