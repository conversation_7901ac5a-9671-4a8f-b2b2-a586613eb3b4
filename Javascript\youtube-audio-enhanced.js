// ==UserScript==
// @name         Youtube Audio HD
// @namespace    https://tampermonkey.com/
// @version      5.1
// @description  HD audio dengan bass jernih, stereo seimbang, dan pengaturan tersimpan otomatis.
// @icon         https://www.google.com/s2/favicons?sz=64&domain=youtube.com
// <AUTHOR>
// @match        https://www.youtube.com/*
// @match        https://music.youtube.com/*
// @match        https://m.youtube.com/*
// @match        https://www.youtube-nocookie.com/*
// @license      MIT
// @grant        GM_getValue
// @grant        GM_setValue
// @grant        GM_registerMenuCommand
// ==/UserScript==
(function () {
  "use strict";

  // Enhanced Audio Constants - Optimized for Crystal Clear Sound with Deep Bass
  const DEFAULT_DELAY = 0.012;
  const DEFAULT_GAIN = 1.15;
  const DEFAULT_THRESHOLD = -18;
  const DEFAULT_RATIO = 3;
  const DEFAULT_ATTACK = 0.002;
  const DEFAULT_RELEASE = 0.08;
  const DEFAULT_REVERB_WET = 0.18;
  const DEFAULT_BASS_BOOST = 1.8;          // Enhanced for small bass clarity
  const DEFAULT_SUB_BASS_BOOST = 2.2;      // New: Deep sub-bass enhancement
  const DEFAULT_MID_BASS_BOOST = 1.6;      // New: Mid-bass punch
  const DEFAULT_TREBLE_BOOST = 1.3;
  const DEFAULT_PRESENCE_BOOST = 1.25;
  const DEFAULT_STEREO_WIDTH = 1.0;        // Balanced stereo - no widening for clear vocals
  const DEFAULT_STEREO_BALANCE = 0.0;      // New: Center balance (-1.0 = left, 0.0 = center, 1.0 = right)
  const DEFAULT_BASS_COMPRESSOR_RATIO = 3; // New: Bass-specific compression
  const DEFAULT_HARMONIC_INTENSITY = 0.4;    // New: Harmonic enhancement
  const DEFAULT_DYNAMIC_RANGE = 1.2;         // New: Dynamic range expansion
  const DEFAULT_PSYCHO_BASS = 0.5;           // New: Psychoacoustic bass
  const DEFAULT_STEREO_DEPTH = 0.3;          // New: Enhanced stereo depth

  const SELECTORS = {
    VIDEO: "video.html5-main-video",
    FALLBACK_VIDEO: "video"
  };

  const RETRY_CONFIG = {
    MAX_ATTEMPTS: 10,
    INITIAL_DELAY: 100,
    MAX_DELAY: 2000
  };

  // Memory monitoring and management class
  class MemoryMonitor {
    constructor() {
      this.memoryUsage = {
        initial: 0,
        current: 0,
        peak: 0,
        audioBuffers: 0,
        nodeCount: 0
      };

      this.thresholds = {
        warning: 50 * 1024 * 1024,  // 50MB
        critical: 100 * 1024 * 1024  // 100MB
      };

      this.startMonitoring();
    }

    startMonitoring() {
      if (!performance.memory) {
        console.warn('Memory monitoring not available in this browser');
        return;
      }

      this.memoryUsage.initial = performance.memory.usedJSHeapSize;

      // Monitor memory every 30 seconds
      this.monitorInterval = setInterval(() => {
        this.checkMemoryUsage();
      }, 30000);
    }

    checkMemoryUsage() {
      if (!performance.memory) return;

      const current = performance.memory.usedJSHeapSize;
      const increase = current - this.memoryUsage.initial;

      this.memoryUsage.current = current;
      this.memoryUsage.peak = Math.max(this.memoryUsage.peak, current);

      if (increase > this.thresholds.critical) {
        console.warn('Critical memory usage detected:', this.formatBytes(increase));
        this.triggerGarbageCollection();
      } else if (increase > this.thresholds.warning) {
        console.warn('High memory usage detected:', this.formatBytes(increase));
      }
    }

    triggerGarbageCollection() {
      // Force garbage collection if available
      if (window.gc) {
        window.gc();
        console.log('Forced garbage collection');
      }

      // Clear any cached data
      this.clearCaches();
    }

    clearCaches() {
      // Signal to clear audio processor caches
      if (window.audioProcessor) {
        window.audioProcessor.clearMemoryCaches();
      }
    }

    formatBytes(bytes) {
      return `${(bytes / 1024 / 1024).toFixed(2)} MB`;
    }

    getReport() {
      if (!performance.memory) return 'Memory monitoring not available';

      return {
        current: this.formatBytes(this.memoryUsage.current),
        peak: this.formatBytes(this.memoryUsage.peak),
        increase: this.formatBytes(this.memoryUsage.current - this.memoryUsage.initial),
        audioBuffers: this.memoryUsage.audioBuffers,
        nodeCount: this.memoryUsage.nodeCount
      };
    }

    destroy() {
      if (this.monitorInterval) {
        clearInterval(this.monitorInterval);
        this.monitorInterval = null;
      }
    }
  }

  // Audio processor class for better organization and performance
  class AudioProcessor {
    constructor() {
      this.audioCtx = null;
      this.source = null;
      this.currentVideo = null;
      this.isInitialized = false;
      this.eventListeners = new Map();

      // Memory management: Node pools and weak references
      this.nodePool = new Map();
      this.activeNodes = new Set();
      this.weakRefs = new WeakMap(); // For tracking DOM elements
      this.memoryMonitor = new MemoryMonitor();

      // Audio processing chain nodes
      this.audioNodes = {
        // Core processing
        delay: null,
        gain: null,
        compressor: null,

        // Reverb system
        reverb: null,
        reverbGain: null,
        dryGain: null,

        // Multi-band bass enhancement
        subBassFilter: null,      // 20-60Hz - Deep sub-bass
        midBassFilter: null,      // 60-150Hz - Mid-bass punch
        bassFilter: null,         // 150-300Hz - Upper bass
        bassCompressor: null,     // Bass-specific compressor

        // Clarity enhancement
        trebleFilter: null,
        presenceFilter: null,     // Essential untuk vocal clarity
        clarityFilter: null,      // High-frequency clarity

        // Stereo processing
        splitter: null,
        merger: null,
        leftGain: null,
        rightGain: null,
        leftDelay: null,
        rightDelay: null,

        // Advanced enhancement engines
        harmonic: null,           // Harmonic enhancement
        dynamic: null,            // Dynamic range expansion
        psychoBass: null,         // Psychoacoustic bass
        stereoDepth: null,        // Enhanced stereo imaging

        // Performance monitoring
        analyser: null,           // Real-time audio analysis
        meter: null              // Audio level metering
      };

      // Optimized settings dengan validasi
      this.settings = this.loadValidatedSettings();

      // Performance optimization: Debouncing and batching
      this.updateTimeout = null;
      this.pendingUpdates = new Set();
      this.batchUpdateInterval = 50; // ms

      // Memory management
      this.reverbBuffer = null;
      this.harmonicCurve = null;

      // Performance monitoring
      this.performanceMetrics = {
        initTime: 0,
        updateCount: 0,
        lastUpdateTime: 0,
        averageUpdateTime: 0
      };
    }

    // Load dan validasi settings dari storage
    loadValidatedSettings() {
      const defaultSettings = {
        delayTime: DEFAULT_DELAY,
        gainValue: DEFAULT_GAIN,
        threshold: DEFAULT_THRESHOLD,
        ratio: DEFAULT_RATIO,
        attack: DEFAULT_ATTACK,
        release: DEFAULT_RELEASE,
        reverbWet: DEFAULT_REVERB_WET,

        // Enhanced Bass System
        subBassBoost: DEFAULT_SUB_BASS_BOOST,
        midBassBoost: DEFAULT_MID_BASS_BOOST,
        bassBoost: DEFAULT_BASS_BOOST,
        bassCompressorRatio: DEFAULT_BASS_COMPRESSOR_RATIO,

        // Clarity Enhancement
        trebleBoost: DEFAULT_TREBLE_BOOST,
        presenceBoost: DEFAULT_PRESENCE_BOOST,

        // Balanced Stereo Enhancement
        stereoWidth: DEFAULT_STEREO_WIDTH,
        stereoBalance: DEFAULT_STEREO_BALANCE,

        // New Enhancements
        harmonicIntensity: DEFAULT_HARMONIC_INTENSITY,
        dynamicRange: DEFAULT_DYNAMIC_RANGE,
        psychoBass: DEFAULT_PSYCHO_BASS,
        stereoDepth: DEFAULT_STEREO_DEPTH
      };

      const validationRules = {
        delayTime: { min: 0.001, max: 0.1 },
        gainValue: { min: 0.1, max: 3.0 },
        threshold: { min: -100, max: 0 },
        ratio: { min: 1, max: 20 },
        attack: { min: 0.001, max: 1 },
        release: { min: 0.01, max: 1 },
        reverbWet: { min: 0, max: 1 },

        // Enhanced Bass Validation
        subBassBoost: { min: 0.5, max: 4.0 },
        midBassBoost: { min: 0.5, max: 3.5 },
        bassBoost: { min: 0.5, max: 3.0 },
        bassCompressorRatio: { min: 1, max: 10 },

        // Clarity Validation
        trebleBoost: { min: 0.5, max: 3.0 },
        presenceBoost: { min: 0.5, max: 3.0 },

        // Balanced Stereo Validation
        stereoWidth: { min: 0.5, max: 2.0 },      // Reduced max for balanced audio
        stereoBalance: { min: -1.0, max: 1.0 },    // New: Balance control

        // New Enhancements Validation
        harmonicIntensity: { min: 0, max: 1 },
        dynamicRange: { min: 1, max: 2 },
        psychoBass: { min: 0, max: 1 },
        stereoDepth: { min: 0, max: 1 }
      };

      const settings = {};
      let hasInvalidValues = false;

      Object.entries(defaultSettings).forEach(([key, defaultValue]) => {
        const storedValue = GM_getValue(key, defaultValue);
        const rule = validationRules[key];

        // Validasi nilai
        if (typeof storedValue === 'number' &&
            isFinite(storedValue) &&
            storedValue >= rule.min &&
            storedValue <= rule.max) {
          settings[key] = storedValue;
        } else {
          console.warn(`Invalid stored value for ${key}: ${storedValue}, using default: ${defaultValue}`);
          settings[key] = defaultValue;
          GM_setValue(key, defaultValue); // Save corrected value
          hasInvalidValues = true;
        }
      });

      if (hasInvalidValues) {
        this.showNotification('Some settings were corrected', 'info');
      }

      return settings;
    }

    // Enhanced initialization with comprehensive error handling and recovery
    async init() {
      try {
        // Check Web Audio API support
        if (!this.checkWebAudioSupport()) {
          throw new Error('Web Audio API not supported in this browser');
        }

        const videoElement = await this.findVideoElement();
        if (!videoElement) {
          throw new Error('No video element found');
        }

        // Initialize with retry mechanism
        await this.initializeWithRetry();
        await this.connectAudio(videoElement);
        this.setupVideoEventListeners(videoElement);

        // Show enhanced status indicator and success notification
        this.createStatusIndicator();
        this.showNotification('🎵 Enhanced Bass + Balanced Stereo Audio Activated! 🔊', 'success');

        console.log('Audio processor initialized successfully');
      } catch (error) {
        console.error('Failed to initialize audio processor:', error);
        await this.handleInitializationError(error);
      }
    }

    checkWebAudioSupport() {
      try {
        const AudioContextClass = window.AudioContext || window['webkitAudioContext'];
        if (!AudioContextClass) {
          return false;
        }

        // Test basic Web Audio API functionality
        const testContext = new AudioContextClass();
        const testGain = testContext.createGain();
        testGain.disconnect(); // Ensure proper cleanup
        testContext.close();

        return true;
      } catch (error) {
        console.error('Web Audio API support check failed:', error);
        return false;
      }
    }

    async initializeWithRetry(maxRetries = 3) {
      let lastError;

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          await this.initializeAudioNodes();
          return; // Success
        } catch (error) {
          lastError = error;
          console.warn(`Audio initialization attempt ${attempt} failed:`, error);

          if (attempt < maxRetries) {
            // Progressive delay between retries
            const delay = attempt * 500;
            await new Promise(resolve => setTimeout(resolve, delay));

            // Clean up before retry
            await this.cleanup();
          }
        }
      }

      throw new Error(`Audio initialization failed after ${maxRetries} attempts: ${lastError.message}`);
    }

    async handleInitializationError(error) {
      try {
        // Attempt graceful degradation
        if (error.message.includes('Web Audio API')) {
          this.showNotification('⚠️ Web Audio API not supported - audio enhancement disabled', 'error');
          return;
        }

        if (error.message.includes('video element')) {
          // Retry finding video element after delay
          setTimeout(async () => {
            try {
              await this.init();
            } catch (retryError) {
              console.error('Retry initialization failed:', retryError);
              this.showNotification('❌ Audio enhancement failed to initialize', 'error');
            }
          }, 2000);
          return;
        }

        // For other errors, show user-friendly message
        this.showNotification('❌ Audio enhancement failed to initialize', 'error');

        // Log detailed error for debugging
        console.error('Detailed initialization error:', {
          message: error.message,
          stack: error.stack,
          audioContext: !!this.audioCtx,
          isInitialized: this.isInitialized,
          activeNodes: this.activeNodes.size
        });

      } catch (handlerError) {
        console.error('Error in initialization error handler:', handlerError);
      }
    }

    // Optimized video element detection with exponential backoff
    async findVideoElement(attempt = 1) {
      const videoElement = document.querySelector(SELECTORS.VIDEO) ||
                          document.querySelector(SELECTORS.FALLBACK_VIDEO);

      if (videoElement) {
        return videoElement;
      }

      if (attempt >= RETRY_CONFIG.MAX_ATTEMPTS) {
        throw new Error("Video element not found after maximum attempts");
      }

      const delay = Math.min(
        RETRY_CONFIG.INITIAL_DELAY * Math.pow(2, attempt - 1),
        RETRY_CONFIG.MAX_DELAY
      );

      await new Promise(resolve => setTimeout(resolve, delay));
      return this.findVideoElement(attempt + 1);
    }

    // Removed duplicate initAudioContext method - functionality merged into initializeAudioNodes

    async initializeAudioNodes() {
      const startTime = performance.now();

      try {
        // Initialize audio context with optimized settings
        if (!this.audioCtx || this.audioCtx.state === 'closed') {
          await this.createOptimizedAudioContext();
        }

        // Performance optimization: Batch node creation
        await this.createAudioNodesBatch();

        // Initialize performance monitoring
        this.setupPerformanceMonitoring();

        // Apply initial settings
        await this.updateAudioNodes();

        this.isInitialized = true;
        this.performanceMetrics.initTime = performance.now() - startTime;
        console.log(`🎵 Enhanced Audio System Initialized in ${this.performanceMetrics.initTime.toFixed(2)}ms`);
      } catch (error) {
        console.error('Error initializing enhanced audio nodes:', error);
        throw error;
      }
    }

    async createOptimizedAudioContext() {
      const AudioContextClass = window.AudioContext || window['webkitAudioContext'];

      // Optimized audio context settings
      const contextOptions = {
        latencyHint: 'interactive',
        sampleRate: 48000, // Optimal for most systems
      };

      this.audioCtx = new AudioContextClass(contextOptions);

      // Resume context immediately if suspended
      if (this.audioCtx.state === 'suspended') {
        await this.audioCtx.resume();
      }
    }

    async createAudioNodesBatch() {
      // Performance optimization: Create nodes in batches to avoid blocking
      const nodeCreationBatches = [
        () => this.createCoreNodes(),
        () => this.createBassEnhancementNodes(),
        () => this.createClarityNodes(),
        () => this.createStereoNodes(),
        () => this.createAdvancedNodes()
      ];

      for (const createBatch of nodeCreationBatches) {
        createBatch();
        // Yield control to prevent blocking
        await new Promise(resolve => setTimeout(resolve, 0));
      }
    }

    createCoreNodes() {
      // Core processing nodes
      this.audioNodes.delay = this.audioCtx.createDelay();
      this.audioNodes.gain = this.audioCtx.createGain();
      this.audioNodes.compressor = this.audioCtx.createDynamicsCompressor();

      // Add to active nodes for tracking
      this.activeNodes.add(this.audioNodes.delay);
      this.activeNodes.add(this.audioNodes.gain);
      this.activeNodes.add(this.audioNodes.compressor);
    }

    createBassEnhancementNodes() {
      // === ENHANCED MULTI-BAND BASS SYSTEM ===
      // Sub-bass filter (20-60Hz) - Makes deep bass audible
      this.audioNodes.subBassFilter = this.audioCtx.createBiquadFilter();
      this.audioNodes.subBassFilter.type = 'peaking';
      this.audioNodes.subBassFilter.frequency.value = 40; // Hz - Deep sub-bass
      this.audioNodes.subBassFilter.Q.value = 2.0; // Narrow band for precision

      // Mid-bass filter (60-150Hz) - Adds punch and warmth
      this.audioNodes.midBassFilter = this.audioCtx.createBiquadFilter();
      this.audioNodes.midBassFilter.type = 'peaking';
      this.audioNodes.midBassFilter.frequency.value = 100; // Hz - Mid-bass punch
      this.audioNodes.midBassFilter.Q.value = 1.5; // Moderate Q for natural sound

      // Upper bass filter (150-300Hz) - Clarity and definition
      this.audioNodes.bassFilter = this.audioCtx.createBiquadFilter();
      this.audioNodes.bassFilter.type = 'peaking';
      this.audioNodes.bassFilter.frequency.value = 200; // Hz - Upper bass clarity
      this.audioNodes.bassFilter.Q.value = 1.0; // Gentle slope

      // Bass-specific compressor for dynamic enhancement
      this.audioNodes.bassCompressor = this.audioCtx.createDynamicsCompressor();
      this.audioNodes.bassCompressor.threshold.value = -25;
      this.audioNodes.bassCompressor.knee.value = 10;
      this.audioNodes.bassCompressor.attack.value = 0.001;
      this.audioNodes.bassCompressor.release.value = 0.1;

      // Add to active nodes
      this.activeNodes.add(this.audioNodes.subBassFilter);
      this.activeNodes.add(this.audioNodes.midBassFilter);
      this.activeNodes.add(this.audioNodes.bassFilter);
      this.activeNodes.add(this.audioNodes.bassCompressor);
    }

    createClarityNodes() {
      // === CLARITY ENHANCEMENT SYSTEM ===
      // Presence filter for vocal clarity
      this.audioNodes.presenceFilter = this.audioCtx.createBiquadFilter();
      this.audioNodes.presenceFilter.type = 'peaking';
      this.audioNodes.presenceFilter.frequency.value = 2800; // Hz - Vocal presence
      this.audioNodes.presenceFilter.Q.value = 1.2; // Focused enhancement

      // Treble filter for sparkle and air
      this.audioNodes.trebleFilter = this.audioCtx.createBiquadFilter();
      this.audioNodes.trebleFilter.type = 'highshelf';
      this.audioNodes.trebleFilter.frequency.value = 8000; // Hz - Treble sparkle
      this.audioNodes.trebleFilter.Q.value = 0.7; // Smooth response

      // High-frequency clarity filter
      this.audioNodes.clarityFilter = this.audioCtx.createBiquadFilter();
      this.audioNodes.clarityFilter.type = 'peaking';
      this.audioNodes.clarityFilter.frequency.value = 12000; // Hz - Air and clarity
      this.audioNodes.clarityFilter.Q.value = 0.8; // Gentle enhancement

      // Add to active nodes
      this.activeNodes.add(this.audioNodes.presenceFilter);
      this.activeNodes.add(this.audioNodes.trebleFilter);
      this.activeNodes.add(this.audioNodes.clarityFilter);
    }

    createStereoNodes() {
      // Stereo processing nodes
      this.audioNodes.splitter = this.audioCtx.createChannelSplitter(2);
      this.audioNodes.merger = this.audioCtx.createChannelMerger(2);
      this.audioNodes.leftGain = this.audioCtx.createGain();
      this.audioNodes.rightGain = this.audioCtx.createGain();
      this.audioNodes.leftDelay = this.audioCtx.createDelay();
      this.audioNodes.rightDelay = this.audioCtx.createDelay();

      // Reverb system
      this.audioNodes.reverb = this.audioCtx.createConvolver();
      this.audioNodes.reverbGain = this.audioCtx.createGain();
      this.audioNodes.dryGain = this.audioCtx.createGain();

      // Create enhanced reverb buffer (cached for performance)
      if (!this.reverbBuffer) {
        this.reverbBuffer = this.createReverbBuffer();
      }
      this.audioNodes.reverb.buffer = this.reverbBuffer;

      // Add to active nodes
      this.activeNodes.add(this.audioNodes.splitter);
      this.activeNodes.add(this.audioNodes.merger);
      this.activeNodes.add(this.audioNodes.leftGain);
      this.activeNodes.add(this.audioNodes.rightGain);
      this.activeNodes.add(this.audioNodes.leftDelay);
      this.activeNodes.add(this.audioNodes.rightDelay);
      this.activeNodes.add(this.audioNodes.reverb);
      this.activeNodes.add(this.audioNodes.reverbGain);
      this.activeNodes.add(this.audioNodes.dryGain);
    }

    createAdvancedNodes() {
      // === NEW HARMONIC ENHANCEMENT ENGINE ===
      this.audioNodes.harmonic = this.audioCtx.createWaveShaper();

      // Create cached harmonic curve for performance
      if (!this.harmonicCurve) {
        this.harmonicCurve = this.createHarmonicCurve(this.settings.harmonicIntensity || DEFAULT_HARMONIC_INTENSITY);
      }
      this.audioNodes.harmonic.curve = this.harmonicCurve;

      // === DYNAMIC RANGE EXPANSION ===
      this.audioNodes.dynamic = this.audioCtx.createDynamicsCompressor();
      this.audioNodes.dynamic.threshold.value = -50;
      this.audioNodes.dynamic.knee.value = 5;
      this.audioNodes.dynamic.ratio.value = 0.8;  // Ratio < 1 for expansion

      // === PSYCHOACOUSTIC BASS ENGINE ===
      this.audioNodes.psychoBass = this.audioCtx.createBiquadFilter();
      this.audioNodes.psychoBass.type = 'peaking';
      this.audioNodes.psychoBass.frequency.value = 55;
      this.audioNodes.psychoBass.Q.value = 2.5;

      // === ENHANCED STEREO DEPTH ===
      this.audioNodes.stereoDepth = this.audioCtx.createStereoPanner();

      // Performance monitoring
      this.audioNodes.analyser = this.audioCtx.createAnalyser();
      this.audioNodes.analyser.fftSize = 2048;
      this.audioNodes.analyser.smoothingTimeConstant = 0.8;

      // Add to active nodes
      this.activeNodes.add(this.audioNodes.harmonic);
      this.activeNodes.add(this.audioNodes.dynamic);
      this.activeNodes.add(this.audioNodes.psychoBass);
      this.activeNodes.add(this.audioNodes.stereoDepth);
      this.activeNodes.add(this.audioNodes.analyser);
    }

    setupPerformanceMonitoring() {
      // Setup real-time performance monitoring
      this.performanceMetrics.updateCount = 0;
      this.performanceMetrics.lastUpdateTime = performance.now();
    }

    // Enhanced reverb buffer for spatial audio
    createReverbBuffer() {
      const sampleRate = this.audioCtx.sampleRate;
      const length = sampleRate * 1.2; // 1.2 second reverb for more spaciousness
      const buffer = this.audioCtx.createBuffer(2, length, sampleRate);

      for (let channel = 0; channel < 2; channel++) {
        const channelData = buffer.getChannelData(channel);
        for (let i = 0; i < length; i++) {
          // Enhanced decay curve for more natural reverb
          const decay = Math.exp(-i / (sampleRate * 0.6));
          const earlyReflection = Math.exp(-i / (sampleRate * 0.1)) * 0.3;

          // Add some stereo variation
          const stereoVariation = channel === 0 ? 1.0 : 0.95;

          channelData[i] = (Math.random() * 2 - 1) * (decay + earlyReflection) * 0.25 * stereoVariation;
        }
      }

      return buffer;
    }

    // Optimized audio node update with batching and performance monitoring
    async updateAudioNodes() {
      const updateStartTime = performance.now();

      if (!this.audioCtx || !this.isInitialized) {
        console.warn('Audio context or nodes not initialized');
        return;
      }

      try {
        // Performance optimization: Batch parameter updates
        const parameterUpdates = [];

        // Helper function for safe setValue with validation and batching
        const safeSetValue = (param, value, paramName, min = -Infinity, max = Infinity) => {
          if (!param || typeof value !== 'number' || !isFinite(value)) {
            console.warn(`Invalid ${paramName} value:`, value);
            return false;
          }

          const clampedValue = Math.max(min, Math.min(max, value));
          if (clampedValue !== value) {
            console.warn(`${paramName} clamped from ${value} to ${clampedValue}`);
          }

          // Batch parameter updates for better performance
          parameterUpdates.push(() => {
            try {
              param.setValueAtTime(clampedValue, this.audioCtx.currentTime);
              return true;
            } catch (error) {
              console.error(`Error setting ${paramName}:`, error);
              return false;
            }
          });

          return true;
        };

        // Update core processing nodes
        if (this.audioNodes.delay) {
          safeSetValue(this.audioNodes.delay.delayTime, this.settings.delayTime || DEFAULT_DELAY, 'delayTime', 0.001, 0.1);
        }

        if (this.audioNodes.gain) {
          safeSetValue(this.audioNodes.gain.gain, this.settings.gainValue || DEFAULT_GAIN, 'gain', 0.1, 3.0);
        }

        if (this.audioNodes.compressor) {
          safeSetValue(this.audioNodes.compressor.threshold, this.settings.threshold || DEFAULT_THRESHOLD, 'threshold', -100, 0);
          safeSetValue(this.audioNodes.compressor.ratio, this.settings.ratio || DEFAULT_RATIO, 'ratio', 1, 20);
          safeSetValue(this.audioNodes.compressor.attack, this.settings.attack || DEFAULT_ATTACK, 'attack', 0.001, 1);
          safeSetValue(this.audioNodes.compressor.release, this.settings.release || DEFAULT_RELEASE, 'release', 0.01, 1);
        }

        // Update reverb settings
        if (this.audioNodes.reverbGain && this.audioNodes.dryGain) {
          const reverbWet = this.settings.reverbWet || DEFAULT_REVERB_WET;
          const dryLevel = 1.0 - reverbWet;

          safeSetValue(this.audioNodes.reverbGain.gain, reverbWet, 'reverbWet', 0, 1);
          safeSetValue(this.audioNodes.dryGain.gain, dryLevel, 'dryLevel', 0, 1);
        }

        // === ENHANCED MULTI-BAND BASS SYSTEM ===
        if (this.audioNodes.subBassFilter) {
          const subBassBoost = this.settings.subBassBoost || DEFAULT_SUB_BASS_BOOST;
          const subBassGain = (subBassBoost - 1) * 12; // More aggressive for sub-bass
          safeSetValue(this.audioNodes.subBassFilter.gain, subBassGain, 'subBassGain', -20, 24);
        }

        if (this.audioNodes.midBassFilter) {
          const midBassBoost = this.settings.midBassBoost || DEFAULT_MID_BASS_BOOST;
          const midBassGain = (midBassBoost - 1) * 10; // Strong mid-bass enhancement
          safeSetValue(this.audioNodes.midBassFilter.gain, midBassGain, 'midBassGain', -20, 20);
        }

        if (this.audioNodes.bassFilter) {
          const bassBoost = this.settings.bassBoost || DEFAULT_BASS_BOOST;
          const bassGain = (bassBoost - 1) * 8; // Upper bass clarity
          safeSetValue(this.audioNodes.bassFilter.gain, bassGain, 'bassGain', -20, 20);
        }

        // Bass compressor for dynamic enhancement
        if (this.audioNodes.bassCompressor) {
          const bassRatio = this.settings.bassCompressorRatio || DEFAULT_BASS_COMPRESSOR_RATIO;
          safeSetValue(this.audioNodes.bassCompressor.ratio, bassRatio, 'bassCompressorRatio', 1, 10);
        }

        // === CLARITY ENHANCEMENT SYSTEM ===
        if (this.audioNodes.presenceFilter) {
          const presenceBoost = this.settings.presenceBoost || DEFAULT_PRESENCE_BOOST;
          const presenceGain = (presenceBoost - 1) * 6; // Vocal clarity
          safeSetValue(this.audioNodes.presenceFilter.gain, presenceGain, 'presenceGain', -20, 20);
        }

        if (this.audioNodes.trebleFilter) {
          const trebleBoost = this.settings.trebleBoost || DEFAULT_TREBLE_BOOST;
          const trebleGain = (trebleBoost - 1) * 8; // Treble sparkle
          safeSetValue(this.audioNodes.trebleFilter.gain, trebleGain, 'trebleGain', -20, 20);
        }

        if (this.audioNodes.clarityFilter) {
          // Auto-adjust clarity based on treble setting
          const clarityGain = (this.settings.trebleBoost - 1) * 4; // Subtle high-freq clarity
          safeSetValue(this.audioNodes.clarityFilter.gain, clarityGain, 'clarityGain', -10, 15);
        }

        // === BALANCED STEREO ENHANCEMENT ===
        if (this.audioNodes.leftGain && this.audioNodes.rightGain) {
          const stereoBalance = this.settings.stereoBalance || DEFAULT_STEREO_BALANCE;

          // Fixed stereo balance calculation
          // stereoBalance: -1.0 = full left, 0.0 = center, 1.0 = full right
          const leftGain = stereoBalance <= 0 ? 1.0 : 1.0 - stereoBalance;   // Full when balance is left/center, reduced when right
          const rightGain = stereoBalance >= 0 ? 1.0 : 1.0 + stereoBalance;  // Full when balance is right/center, reduced when left

          safeSetValue(this.audioNodes.leftGain.gain, leftGain, 'leftGain', 0, 1);
          safeSetValue(this.audioNodes.rightGain.gain, rightGain, 'rightGain', 0, 1);
        }

        // Minimal stereo width processing (only if width > 1.0)
        if (this.audioNodes.leftDelay && this.audioNodes.rightDelay) {
          const stereoWidth = this.settings.stereoWidth || DEFAULT_STEREO_WIDTH;
          if (stereoWidth > 1.0) {
            const widthDelay = (stereoWidth - 1.0) * 0.003; // Reduced delay for subtle effect
            safeSetValue(this.audioNodes.leftDelay.delayTime, widthDelay, 'leftDelay', 0, 0.02);
            safeSetValue(this.audioNodes.rightDelay.delayTime, widthDelay, 'rightDelay', 0, 0.02);
          } else {
            // No delay for balanced stereo
            safeSetValue(this.audioNodes.leftDelay.delayTime, 0, 'leftDelay', 0, 0.02);
            safeSetValue(this.audioNodes.rightDelay.delayTime, 0, 'rightDelay', 0, 0.02);
          }
        }

        // Update advanced engine parameters
        if (this.audioNodes.harmonic) {
          const harmonicIntensity = this.settings.harmonicIntensity || DEFAULT_HARMONIC_INTENSITY;
          // Performance optimization: Only recreate curve if intensity changed
          if (!this.harmonicCurve || this.lastHarmonicIntensity !== harmonicIntensity) {
            this.harmonicCurve = this.createHarmonicCurve(harmonicIntensity);
            this.audioNodes.harmonic.curve = this.harmonicCurve;
            this.lastHarmonicIntensity = harmonicIntensity;
          }
        }

        if (this.audioNodes.dynamic) {
          const dynamicRange = this.settings.dynamicRange || DEFAULT_DYNAMIC_RANGE;
          safeSetValue(this.audioNodes.dynamic.ratio, 1 / dynamicRange, 'dynamicRatio', 0.1, 10);
        }

        if (this.audioNodes.psychoBass) {
          const psychoBass = this.settings.psychoBass || DEFAULT_PSYCHO_BASS;
          safeSetValue(this.audioNodes.psychoBass.gain, psychoBass * 12, 'psychoBassGain', 0, 24);
        }

        if (this.audioNodes.stereoDepth) {
          const stereoDepth = this.settings.stereoDepth || DEFAULT_STEREO_DEPTH;
          safeSetValue(this.audioNodes.stereoDepth.pan, 0, 'stereoDepthPan', -1, 1);  // Center
          // Apply modulation for enhanced depth
          this.modulateStereoDepth(stereoDepth);
        }

        // Performance optimization: Execute all parameter updates in batch
        parameterUpdates.forEach(update => update());

        // Update performance metrics
        const updateTime = performance.now() - updateStartTime;
        this.performanceMetrics.updateCount++;
        this.performanceMetrics.lastUpdateTime = updateTime;
        this.performanceMetrics.averageUpdateTime =
          (this.performanceMetrics.averageUpdateTime * (this.performanceMetrics.updateCount - 1) + updateTime) /
          this.performanceMetrics.updateCount;

        console.log(`Audio settings updated successfully in ${updateTime.toFixed(2)}ms`);
      } catch (error) {
        console.error('Error updating audio nodes:', error);
      }
    }

    // Removed updateSpatialAudioNodes method - functionality integrated into updateAudioNodes

    // Connect audio nodes to video element
    async connectAudio(videoElement) {
      try {
        // Always cleanup and recreate audio context for new video
        await this.cleanup();
        
        if (!this.audioCtx || this.audioCtx.state === 'closed') {
          const AudioContextClass = window.AudioContext || window['webkitAudioContext'];
          this.audioCtx = new AudioContextClass();
        }

        // Create new source for this video
        this.source = this.audioCtx.createMediaElementSource(videoElement);
        this.currentVideo = videoElement;

        // Initialize audio nodes with new context
        await this.initializeAudioNodes();

        // Connect processing chain
        await this.connectProcessingChain();

        // Setup event listeners
        this.setupVideoEventListeners(videoElement);

        // Resume audio context if needed
        if (this.audioCtx.state === 'suspended') {
          await this.audioCtx.resume();
        }

      } catch (error) {
        console.error("Failed to connect audio:", error);
        // Fallback to direct output on error
        if (this.source) {
          this.source.connect(this.audioCtx.destination);
        }
      }
    }

    async connectProcessingChain() {
      try {
        // Performance optimization: Validate all nodes before connecting
        if (!this.validateAudioNodes()) {
          throw new Error('Audio nodes validation failed');
        }

        // Core processing chain
        this.source
          .connect(this.audioNodes.gain)
          .connect(this.audioNodes.compressor);

        // Bass enhancement chain
        this.audioNodes.compressor
          .connect(this.audioNodes.subBassFilter)
          .connect(this.audioNodes.midBassFilter)
          .connect(this.audioNodes.bassFilter)
          .connect(this.audioNodes.bassCompressor);

        // Clarity chain
        this.audioNodes.bassCompressor
          .connect(this.audioNodes.presenceFilter)
          .connect(this.audioNodes.trebleFilter)
          .connect(this.audioNodes.clarityFilter);

        // Split for stereo processing
        this.audioNodes.clarityFilter.connect(this.audioNodes.splitter);

        // Process each channel separately
        this.audioNodes.splitter.connect(this.audioNodes.leftGain, 0);
        this.audioNodes.splitter.connect(this.audioNodes.rightGain, 1);

        this.audioNodes.leftGain.connect(this.audioNodes.leftDelay);
        this.audioNodes.rightGain.connect(this.audioNodes.rightDelay);

        // Mix processed channels
        this.audioNodes.leftDelay.connect(this.audioNodes.merger, 0, 0);  // Left -> Left
        this.audioNodes.rightDelay.connect(this.audioNodes.merger, 0, 1); // Right -> Right

        // Final effects chain with performance monitoring
        this.audioNodes.merger
          .connect(this.audioNodes.harmonic)
          .connect(this.audioNodes.dynamic)
          .connect(this.audioNodes.psychoBass)
          .connect(this.audioNodes.stereoDepth)
          .connect(this.audioNodes.analyser)  // Add analyser for monitoring
          .connect(this.audioCtx.destination);

        console.log('🎵 Optimized audio processing chain connected successfully');
      } catch (error) {
        console.error('Failed to connect processing chain:', error);
        // Enhanced fallback chain with basic processing
        this.connectFallbackChain();
      }
    }

    validateAudioNodes() {
      const requiredNodes = [
        'gain', 'compressor', 'subBassFilter', 'midBassFilter', 'bassFilter',
        'bassCompressor', 'presenceFilter', 'trebleFilter', 'clarityFilter',
        'splitter', 'merger', 'leftGain', 'rightGain', 'leftDelay', 'rightDelay',
        'harmonic', 'dynamic', 'psychoBass', 'stereoDepth', 'analyser'
      ];

      for (const nodeName of requiredNodes) {
        if (!this.audioNodes[nodeName]) {
          console.error(`Missing audio node: ${nodeName}`);
          return false;
        }
      }
      return true;
    }

    connectFallbackChain() {
      try {
        // Simple but effective fallback processing chain
        if (this.audioNodes.gain && this.audioNodes.compressor) {
          this.source
            .connect(this.audioNodes.gain)
            .connect(this.audioNodes.compressor);

          if (this.audioNodes.bassFilter) {
            this.audioNodes.compressor
              .connect(this.audioNodes.bassFilter)
              .connect(this.audioCtx.destination);
          } else {
            this.audioNodes.compressor.connect(this.audioCtx.destination);
          }
        } else {
          // Ultimate fallback - direct connection
          this.source.connect(this.audioCtx.destination);
        }
        console.log('🔄 Fallback audio chain connected');
      } catch (error) {
        console.error('Failed to connect fallback chain:', error);
        // Last resort - direct connection
        if (this.source) {
          this.source.connect(this.audioCtx.destination);
        }
      }
    }

    // Helper method to reset audio context
    async resetAudioContext() {
      if (this.audioCtx && this.audioCtx.state !== 'closed') {
        await this.audioCtx.close();
      }
      this.audioCtx = new (window.AudioContext || window['webkitAudioContext'])();
    }

    // Setup event listeners for video element
    setupVideoEventListeners(videoElement) {
      // Clean up existing listeners
      this.removeVideoEventListeners();

      const listeners = {
        'ended': () => this.cleanup(),
        'pause': () => this.handleVideoPause(),
        'play': () => this.handleVideoPlay(),
        'loadstart': () => this.handleVideoChange()
      };

      Object.entries(listeners).forEach(([event, handler]) => {
        videoElement.addEventListener(event, handler);
        this.eventListeners.set(`${event}_${videoElement}`, { element: videoElement, event, handler });
      });
    }

    // Remove video event listeners with error handling
    removeVideoEventListeners() {
      try {
        this.eventListeners.forEach(({ element, event, handler }) => {
          if (element && typeof element.removeEventListener === 'function') {
            element.removeEventListener(event, handler);
          }
        });
        this.eventListeners.clear();
      } catch (error) {
        console.error('Error removing video event listeners:', error);
        // Force clear the map even if removal fails
        this.eventListeners.clear();
      }
    }

    // Handle video pause
    handleVideoPause() {
      if (this.audioCtx && this.audioCtx.state === 'running') {
        this.audioCtx.suspend().catch(console.error);
      }
    }

    // Handle video play
    async handleVideoPlay() {
      if (this.audioCtx && this.audioCtx.state === 'suspended') {
        try {
          await this.audioCtx.resume();
        } catch (error) {
          console.error("Failed to resume audio context:", error);
        }
      }
    }

    // Handle video change (new video loaded)
    async handleVideoChange() {
      try {
        const videoElement = await this.findVideoElement();
        if (videoElement !== this.currentVideo) {
          await this.connectAudio(videoElement);
        }
      } catch (error) {
        console.error("Failed to handle video change:", error);
      }
    }

    // Enhanced cleanup with better error handling and memory optimization
    async cleanup() {
      try {
        // Clear any pending timeouts
        if (this.updateTimeout) {
          clearTimeout(this.updateTimeout);
          this.updateTimeout = null;
        }

        // Remove event listeners first
        this.removeVideoEventListeners();

        // Disconnect and clean up audio source
        if (this.source) {
          try {
            this.source.disconnect();
          } catch (error) {
            console.warn('Error disconnecting audio source:', error);
          }
          this.source = null;
        }

        // Performance optimization: Batch disconnect all active nodes
        this.disconnectAllNodes();

        // Close audio context
        if (this.audioCtx && this.audioCtx.state !== 'closed') {
          try {
            await this.audioCtx.close();
          } catch (error) {
            console.warn('Error closing audio context:', error);
          }
        }

        // Memory optimization: Clear all node references
        this.clearNodeReferences();

        // Clear cached buffers and curves
        this.reverbBuffer = null;
        this.harmonicCurve = null;
        this.lastHarmonicIntensity = null;

        // Clear performance metrics
        this.performanceMetrics = {
          initTime: 0,
          updateCount: 0,
          lastUpdateTime: 0,
          averageUpdateTime: 0
        };

        this.currentVideo = null;
        this.isInitialized = false;

        console.log('Audio processor cleanup completed successfully');
      } catch (error) {
        console.error("Cleanup error:", error);
      }
    }

    disconnectAllNodes() {
      // Performance optimization: Batch disconnect all nodes
      this.activeNodes.forEach(node => {
        try {
          if (node && typeof node.disconnect === 'function') {
            node.disconnect();
          }
        } catch (error) {
          console.warn('Error disconnecting node:', error);
        }
      });
      this.activeNodes.clear();
    }

    clearNodeReferences() {
      // Memory optimization: Clear all node references in batch
      Object.keys(this.audioNodes).forEach(key => {
        this.audioNodes[key] = null;
      });

      this.audioCtx = null;
      this.nodePool.clear();
    }

    // Update settings with validation and non-blocking notifications
    updateSetting(key, value, validator, unit = '') {
      if (!validator(value)) {
        this.showNotification(`Nilai ${key} tidak valid`, 'error');
        return false;
      }

      this.settings[key] = value;
      GM_setValue(key, value);
      this.updateAudioNodes();
      this.showNotification(`${key} diatur ke ${value}${unit}`, 'success');
      return true;
    }

    // Save all current settings to persistent storage
    saveAllSettings() {
      try {
        Object.entries(this.settings).forEach(([key, value]) => {
          GM_setValue(key, value);
        });
        this.showNotification('🔄 Pengaturan tersimpan otomatis!', 'success');
        console.log('All settings saved to persistent storage');
      } catch (error) {
        console.error('Error saving settings:', error);
        this.showNotification('❌ Gagal menyimpan pengaturan', 'error');
      }
    }

    // Enhanced notification system with better styling
    showNotification(message, type = 'info') {
      // Remove existing notifications
      const existingNotifications = document.querySelectorAll('.audio-processor-notification');
      existingNotifications.forEach(notif => notif.remove());

      // Create notification element
      const notification = document.createElement('div');
      notification.className = 'audio-processor-notification';
      notification.innerHTML = `
        <div class="notification-icon">
          ${type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️'}
        </div>
        <div class="notification-message">${message}</div>
      `;

      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 16px 20px;
        border-radius: 12px;
        color: white;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-size: 14px;
        font-weight: 500;
        z-index: 10001;
        display: flex;
        align-items: center;
        gap: 12px;
        min-width: 280px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        animation: slideInRight 0.3s ease-out;
        background: ${type === 'error' ?
          'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)' :
          type === 'success' ?
          'linear-gradient(135deg, #4caf50 0%, #45a049 100%)' :
          'linear-gradient(135deg, #2196f3 0%, #1976d2 100%)'};
      `;

      // Add animation styles
      const style = document.createElement('style');
      style.textContent = `
        @keyframes slideInRight {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }

        @keyframes slideOutRight {
          from {
            transform: translateX(0);
            opacity: 1;
          }
          to {
            transform: translateX(100%);
            opacity: 0;
          }
        }

        .notification-icon {
          font-size: 18px;
        }

        .notification-message {
          flex: 1;
        }
      `;

      document.head.appendChild(style);
      document.body.appendChild(notification);

      // Auto remove after 4 seconds with animation
      setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease-in forwards';
        setTimeout(() => {
          if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
          }
          if (style.parentNode) {
            style.parentNode.removeChild(style);
          }
        }, 300);
      }, 4000);
    }

    // Enhanced visual status indicator with real-time audio visualization
    createStatusIndicator() {
      // Remove existing indicator
      const existing = document.getElementById('audio-processor-status');
      if (existing) existing.remove();

      const indicator = document.createElement('div');
      indicator.id = 'audio-processor-status';
      indicator.innerHTML = `
        <div class="status-icon">🎵</div>
        <div class="status-content">
          <div class="status-text">Audio Enhanced</div>
          <div class="audio-visualizer">
            <canvas id="audio-viz-canvas" width="60" height="20"></canvas>
          </div>
        </div>
        <div class="status-controls">
          <button class="mini-btn" id="toggle-viz" title="Toggle Visualizer">📊</button>
          <button class="mini-btn" id="quick-settings" title="Quick Settings">⚙️</button>
        </div>
      `;

      indicator.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        padding: 12px 16px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 25px;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-size: 12px;
        font-weight: 600;
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: 12px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        cursor: pointer;
        transition: all 0.3s ease;
        opacity: 0.9;
        min-width: 200px;
      `;

      // Add enhanced styling
      const style = document.createElement('style');
      style.textContent = `
        .status-content {
          display: flex;
          flex-direction: column;
          gap: 4px;
          flex: 1;
        }

        .status-controls {
          display: flex;
          gap: 6px;
        }

        .mini-btn {
          background: rgba(255, 255, 255, 0.2);
          border: none;
          border-radius: 50%;
          width: 24px;
          height: 24px;
          color: white;
          cursor: pointer;
          font-size: 10px;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .mini-btn:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: scale(1.1);
        }

        .audio-visualizer {
          height: 20px;
          border-radius: 4px;
          overflow: hidden;
          background: rgba(0, 0, 0, 0.2);
        }

        #audio-viz-canvas {
          width: 100%;
          height: 100%;
        }
      `;

      document.head.appendChild(style);
      document.body.appendChild(indicator);

      // Setup visualizer
      this.setupAudioVisualizer();

      // Event listeners
      indicator.addEventListener('mouseenter', () => {
        indicator.style.opacity = '1';
        indicator.style.transform = 'scale(1.02)';
      });

      indicator.addEventListener('mouseleave', () => {
        indicator.style.opacity = '0.9';
        indicator.style.transform = 'scale(1)';
      });

      indicator.querySelector('#toggle-viz').addEventListener('click', (e) => {
        e.stopPropagation();
        this.toggleVisualizer();
      });

      indicator.querySelector('#quick-settings').addEventListener('click', (e) => {
        e.stopPropagation();
        this.showQuickSettings();
      });

      indicator.addEventListener('click', () => {
        this.showSpatialAudioDialog();
      });

      // Auto-hide after 15 seconds (extended for enhanced features)
      setTimeout(() => {
        if (indicator.parentNode) {
          indicator.style.opacity = '0.7';
          // Keep visible but more transparent
        }
      }, 15000);
    }

    setupAudioVisualizer() {
      const canvas = document.getElementById('audio-viz-canvas');
      if (!canvas || !this.audioNodes.analyser) return;

      const ctx = canvas.getContext('2d');
      const bufferLength = this.audioNodes.analyser.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);

      this.visualizerActive = true;

      const draw = () => {
        if (!this.visualizerActive || !this.audioNodes.analyser) return;

        requestAnimationFrame(draw);

        this.audioNodes.analyser.getByteFrequencyData(dataArray);

        ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        const barWidth = canvas.width / bufferLength * 2.5;
        let barHeight;
        let x = 0;

        // Create gradient
        const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
        gradient.addColorStop(0, '#ff6b6b');
        gradient.addColorStop(0.5, '#4ecdc4');
        gradient.addColorStop(1, '#45b7d1');

        for (let i = 0; i < bufferLength; i++) {
          barHeight = (dataArray[i] / 255) * canvas.height;

          ctx.fillStyle = gradient;
          ctx.fillRect(x, canvas.height - barHeight, barWidth, barHeight);

          x += barWidth + 1;
        }
      };

      draw();
    }

    toggleVisualizer() {
      this.visualizerActive = !this.visualizerActive;
      const canvas = document.getElementById('audio-viz-canvas');
      if (canvas) {
        canvas.style.display = this.visualizerActive ? 'block' : 'none';
      }

      if (this.visualizerActive) {
        this.setupAudioVisualizer();
      }
    }

    showQuickSettings() {
      const quickMenu = document.createElement('div');
      quickMenu.id = 'quick-settings-menu';
      quickMenu.innerHTML = `
        <div class="quick-menu-item" data-action="bass">🔊 Bass Boost</div>
        <div class="quick-menu-item" data-action="clarity">✨ Clarity</div>
        <div class="quick-menu-item" data-action="stereo">🎭 Stereo</div>
        <div class="quick-menu-item" data-action="presets">🎵 Presets</div>
      `;

      quickMenu.style.cssText = `
        position: fixed;
        bottom: 70px;
        right: 20px;
        background: rgba(0, 0, 0, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 8px;
        padding: 8px;
        z-index: 10001;
        min-width: 120px;
        animation: slideUp 0.2s ease;
      `;

      const menuStyle = document.createElement('style');
      menuStyle.textContent = `
        @keyframes slideUp {
          from { transform: translateY(20px); opacity: 0; }
          to { transform: translateY(0); opacity: 1; }
        }

        .quick-menu-item {
          padding: 8px 12px;
          color: white;
          cursor: pointer;
          border-radius: 4px;
          transition: background 0.2s ease;
          font-size: 12px;
        }

        .quick-menu-item:hover {
          background: rgba(255, 255, 255, 0.1);
        }
      `;

      document.head.appendChild(menuStyle);
      document.body.appendChild(quickMenu);

      // Event listeners
      quickMenu.addEventListener('click', (e) => {
        const action = e.target.dataset.action;
        if (action) {
          this.handleQuickAction(action);
          quickMenu.remove();
          menuStyle.remove();
        }
      });

      // Auto-remove after 5 seconds
      setTimeout(() => {
        if (quickMenu.parentNode) {
          quickMenu.remove();
          menuStyle.remove();
        }
      }, 5000);

      // Remove on click outside
      document.addEventListener('click', function removeMenu(e) {
        if (!quickMenu.contains(e.target)) {
          quickMenu.remove();
          menuStyle.remove();
          document.removeEventListener('click', removeMenu);
        }
      });
    }

    handleQuickAction(action) {
      switch (action) {
        case 'bass':
          this.applyPreset('bassBoost');
          break;
        case 'clarity':
          this.applyPreset('superStereo');
          break;
        case 'stereo':
          this.showSpatialAudioDialog();
          break;
        case 'presets':
          this.showPresetMenu();
          break;
      }
    }

    showPresetMenu() {
      // Quick preset selection
      const presets = ['dolby', 'superStereo', 'bassBoost', 'headphone', 'concertHall'];
      const presetNames = ['🎭 Dolby', '🎪 Clear', '💫 Bass', '🎧 Studio', '🏛️ Hall'];

      const menu = presets.map((preset, index) =>
        `<div class="quick-menu-item" onclick="audioProcessor.applyPreset('${preset}')">${presetNames[index]}</div>`
      ).join('');

      const presetMenu = document.createElement('div');
      presetMenu.innerHTML = menu;
      presetMenu.style.cssText = `
        position: fixed;
        bottom: 70px;
        right: 150px;
        background: rgba(0, 0, 0, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 8px;
        padding: 8px;
        z-index: 10001;
        animation: slideUp 0.2s ease;
      `;

      document.body.appendChild(presetMenu);

      setTimeout(() => presetMenu.remove(), 5000);
    }

    // === ENHANCED PRESET SYSTEM - Optimized for Deep Bass Clarity ===
    applyPreset(presetName) {
      const presets = {
        dolby: {
          delayTime: 0.010,
          gainValue: 1.4,
          threshold: -16,
          ratio: 6,
          attack: 0.002,
          release: 0.08,
          stereoWidth: 1.2,       // Balanced stereo with minimal widening
          stereoBalance: 0.0,     // Centered balance
          reverbWet: 0.22,
          // Enhanced Multi-Band Bass
          subBassBoost: 2.5,      // Deep sub-bass for impact
          midBassBoost: 1.8,      // Mid-bass punch
          bassBoost: 1.6,         // Upper bass clarity
          bassCompressorRatio: 4, // Dynamic bass enhancement
          // Crystal Clarity
          trebleBoost: 1.35,
          presenceBoost: 1.3
        },
        superStereo: {
          delayTime: 0.008,
          gainValue: 1.35,
          threshold: -18,
          ratio: 5,
          attack: 0.001,
          release: 0.06,
          stereoWidth: 1.0,       // Balanced stereo - no widening for clear vocals
          stereoBalance: 0.0,     // Centered balance
          reverbWet: 0.18,
          // Aggressive Bass Enhancement
          subBassBoost: 2.8,      // Maximum sub-bass
          midBassBoost: 2.0,      // Strong mid-bass
          bassBoost: 1.7,         // Clear upper bass
          bassCompressorRatio: 5, // Strong bass compression
          // Bright Clarity
          trebleBoost: 1.4,
          presenceBoost: 1.35
        },
        headphone: {
          delayTime: 0.012,
          gainValue: 1.3,
          threshold: -20,
          ratio: 4,
          attack: 0.003,
          release: 0.1,
          stereoWidth: 1.0,       // Balanced stereo for headphones
          stereoBalance: 0.0,     // Centered balance
          reverbWet: 0.2,
          // Balanced Bass for Headphones
          subBassBoost: 2.2,      // Controlled sub-bass
          midBassBoost: 1.7,      // Warm mid-bass
          bassBoost: 1.5,         // Clean upper bass
          bassCompressorRatio: 3, // Gentle bass compression
          // Smooth Clarity
          trebleBoost: 1.25,
          presenceBoost: 1.2
        },
        concertHall: {
          delayTime: 0.015,
          gainValue: 1.25,
          threshold: -14,
          ratio: 7,
          attack: 0.004,
          release: 0.12,
          stereoWidth: 1.1,       // Minimal width for spacious feel
          stereoBalance: 0.0,     // Centered balance
          reverbWet: 0.35,        // Rich reverb
          // Natural Bass Response
          subBassBoost: 2.0,      // Natural sub-bass
          midBassBoost: 1.6,      // Balanced mid-bass
          bassBoost: 1.4,         // Subtle upper bass
          bassCompressorRatio: 2, // Light bass compression
          // Natural Clarity
          trebleBoost: 1.2,
          presenceBoost: 1.15
        },
        bassBoost: {              // New: Dedicated bass preset
          delayTime: 0.008,
          gainValue: 1.5,
          threshold: -22,
          ratio: 3,
          attack: 0.001,
          release: 0.05,
          stereoWidth: 1.0,       // Balanced stereo for bass focus
          stereoBalance: 0.0,     // Centered balance
          reverbWet: 0.15,
          // Maximum Bass Enhancement
          subBassBoost: 3.2,      // Extreme sub-bass
          midBassBoost: 2.5,      // Powerful mid-bass
          bassBoost: 2.0,         // Strong upper bass
          bassCompressorRatio: 6, // Aggressive bass compression
          // Balanced Clarity
          trebleBoost: 1.1,
          presenceBoost: 1.0
        }
      };

      const preset = presets[presetName];
      if (!preset) {
        this.showNotification(`Preset ${presetName} tidak ditemukan`, 'error');
        return;
      }

      // Apply preset settings dengan validasi
      Object.entries(preset).forEach(([key, value]) => {
        // Validasi nilai sebelum diterapkan
        if (typeof value === 'number' && isFinite(value)) {
          this.settings[key] = value;
          GM_setValue(key, value);
        } else {
          console.warn(`Invalid preset value for ${key}:`, value);
        }
      });

      this.updateAudioNodes();
      this.saveAllSettings(); // Auto-save settings
      this.showNotification(`Preset ${presetName} diterapkan dan tersimpan`, 'success');
    }

    // === MODERN UI DIALOGS ===
    createModernDialog(title, content) {
      // Remove existing dialog if any
      const existingDialog = document.getElementById('audio-processor-dialog');
      if (existingDialog) {
        existingDialog.remove();
      }

      const dialog = document.createElement('div');
      dialog.id = 'audio-processor-dialog';
      dialog.innerHTML = `
        <div class="dialog-overlay">
          <div class="dialog-container">
            <div class="dialog-header">
              <h3>${title}</h3>
              <button class="dialog-close">×</button>
            </div>
            <div class="dialog-content">
              ${content}
            </div>
          </div>
        </div>
      `;

      // Add modern styling
      const style = document.createElement('style');
      style.textContent = `
        #audio-processor-dialog {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 10000;
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .dialog-overlay {
          background: rgba(0, 0, 0, 0.7);
          backdrop-filter: blur(5px);
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          animation: fadeIn 0.3s ease;
        }

        .dialog-container {
          background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%);
          border-radius: 12px;
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
          max-width: 500px;
          width: 90%;
          max-height: 80vh;
          overflow: hidden;
          animation: slideIn 0.3s ease;
        }

        .dialog-header {
          background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
          color: white;
          padding: 20px;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .dialog-header h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
        }

        .dialog-close {
          background: none;
          border: none;
          color: white;
          font-size: 24px;
          cursor: pointer;
          padding: 0;
          width: 30px;
          height: 30px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: background 0.2s;
        }

        .dialog-close:hover {
          background: rgba(255, 255, 255, 0.2);
        }

        .dialog-content {
          padding: 20px;
          color: #e0e0e0;
          max-height: 60vh;
          overflow-y: auto;
        }

        .setting-group {
          margin-bottom: 20px;
          padding: 15px;
          background: rgba(255, 255, 255, 0.05);
          border-radius: 8px;
          border-left: 4px solid #ff6b6b;
        }

        .setting-group h4 {
          margin: 0 0 15px 0;
          color: #ff6b6b;
          font-size: 16px;
        }

        .setting-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
        }

        .setting-item:last-child {
          margin-bottom: 0;
        }

        .setting-label {
          flex: 1;
          margin-right: 15px;
          font-size: 14px;
        }

        .setting-control {
          display: flex;
          align-items: center;
          gap: 10px;
        }

        .setting-slider {
          width: 120px;
          height: 6px;
          border-radius: 3px;
          background: #444;
          outline: none;
          -webkit-appearance: none;
        }

        .setting-slider::-webkit-slider-thumb {
          -webkit-appearance: none;
          width: 18px;
          height: 18px;
          border-radius: 50%;
          background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
          cursor: pointer;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .setting-value {
          min-width: 50px;
          text-align: center;
          font-size: 12px;
          color: #ff6b6b;
          font-weight: 600;
        }

        .preset-buttons {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
          gap: 10px;
          margin-top: 20px;
        }

        .preset-btn {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border: none;
          padding: 12px 16px;
          border-radius: 8px;
          cursor: pointer;
          font-size: 14px;
          font-weight: 500;
          transition: all 0.2s;
        }

        .preset-btn:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        @keyframes slideIn {
          from { transform: translateY(-50px) scale(0.9); opacity: 0; }
          to { transform: translateY(0) scale(1); opacity: 1; }
        }
      `;

      document.head.appendChild(style);
      document.body.appendChild(dialog);

      // Enhanced event listeners dengan cleanup
      const closeDialog = () => {
        // Cleanup slider listeners
        const sliders = dialog.querySelectorAll('.setting-slider');
        sliders.forEach(slider => {
          if (slider._handleInput) {
            slider.removeEventListener('input', slider._handleInput);
            delete slider._handleInput;
          }
        });

        // Remove dialog and style
        dialog.remove();
        style.remove();
      };

      dialog.querySelector('.dialog-close').onclick = closeDialog;

      dialog.querySelector('.dialog-overlay').onclick = (e) => {
        if (e.target === e.currentTarget) {
          closeDialog();
        }
      };

      // Close on Escape key
      const handleKeydown = (e) => {
        if (e.key === 'Escape') {
          closeDialog();
          document.removeEventListener('keydown', handleKeydown);
        }
      };
      document.addEventListener('keydown', handleKeydown);

      return dialog;
    }

    // === DIALOG METHODS ===
    showSpatialAudioDialog() {
      const content = `
        <div class="setting-group">
          <h4>🎭 Balanced Stereo Control</h4>
          <div class="setting-item">
            <span class="setting-label">Stereo Balance</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="stereoBalance" min="-1" max="1" step="0.1" value="${this.settings.stereoBalance}">
              <span class="setting-value" id="stereoBalanceValue">${this.settings.stereoBalance > 0 ? 'R' + this.settings.stereoBalance : this.settings.stereoBalance < 0 ? 'L' + Math.abs(this.settings.stereoBalance) : 'Center'}</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Stereo Width</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="stereoWidth" min="1" max="1.5" step="0.1" value="${this.settings.stereoWidth}">
              <span class="setting-value" id="stereoWidthValue">${this.settings.stereoWidth}x</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Reverb Amount</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="reverbWet" min="0" max="0.5" step="0.05" value="${this.settings.reverbWet}">
              <span class="setting-value" id="reverbWetValue">${this.settings.reverbWet}</span>
            </div>
          </div>
        </div>

        <div class="setting-group">
          <h4>🔊 Multi-Band Bass Enhancement</h4>
          <div class="setting-item">
            <span class="setting-label">Sub-Bass (20-60Hz)</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="subBassBoost" min="1.0" max="3.5" step="0.1" value="${this.settings.subBassBoost}">
              <span class="setting-value" id="subBassBoostValue">${this.settings.subBassBoost}x</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Mid-Bass (60-150Hz)</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="midBassBoost" min="1.0" max="3.0" step="0.1" value="${this.settings.midBassBoost}">
              <span class="setting-value" id="midBassBoostValue">${this.settings.midBassBoost}x</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Upper Bass (150-300Hz)</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="bassBoost" min="1.0" max="2.5" step="0.1" value="${this.settings.bassBoost}">
              <span class="setting-value" id="bassBoostValue">${this.settings.bassBoost}x</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Bass Compressor</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="bassCompressorRatio" min="1" max="8" step="0.5" value="${this.settings.bassCompressorRatio}">
              <span class="setting-value" id="bassCompressorRatioValue">${this.settings.bassCompressorRatio}:1</span>
            </div>
          </div>
        </div>

        <div class="setting-group">
          <h4>✨ Clarity Enhancement</h4>
          <div class="setting-item">
            <span class="setting-label">Presence (Vocals)</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="presenceBoost" min="0.8" max="2.5" step="0.1" value="${this.settings.presenceBoost}">
              <span class="setting-value" id="presenceBoostValue">${this.settings.presenceBoost}x</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Treble (Sparkle)</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="trebleBoost" min="0.8" max="2.5" step="0.1" value="${this.settings.trebleBoost}">
              <span class="setting-value" id="trebleBoostValue">${this.settings.trebleBoost}x</span>
            </div>
          </div>
        </div>

        <div class="preset-buttons">
          <button class="preset-btn" onclick="audioProcessor.applyPreset('dolby')">🎭 Dolby Balanced</button>
          <button class="preset-btn" onclick="audioProcessor.applyPreset('superStereo')">🎪 Clear Vocals</button>
          <button class="preset-btn" onclick="audioProcessor.applyPreset('bassBoost')">🔊 Bass Boost</button>
          <button class="preset-btn" onclick="audioProcessor.applyPreset('headphone')">🎧 Headphone</button>
          <button class="preset-btn" onclick="audioProcessor.applyPreset('concertHall')">🎵 Concert Hall</button>
        </div>
      `;

      const dialog = this.createModernDialog('🎛️ Audio Enhancement', content);
      this.setupSliderListeners(dialog);
      this.initializeSliderValues(dialog);
    }

    showBasicAudioDialog() {
      const content = `
        <div class="setting-group">
          <h4>🔊 Basic Controls</h4>
          <div class="setting-item">
            <span class="setting-label">Audio Gain</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="gainValue" min="0.8" max="2" step="0.1" value="${this.settings.gainValue}">
              <span class="setting-value" id="gainValueValue">${this.settings.gainValue}x</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Audio Delay</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="delayTime" min="0.005" max="0.05" step="0.005" value="${this.settings.delayTime}">
              <span class="setting-value" id="delayTimeValue">${this.settings.delayTime}s</span>
            </div>
          </div>
        </div>
      `;

      const dialog = this.createModernDialog('🔧 Basic Audio Settings', content);
      this.setupSliderListeners(dialog);
      this.initializeSliderValues(dialog);
    }

    showAdvancedDialog() {
      const content = `
        <div class="setting-group">
          <h4>🎛️ Compressor Settings</h4>
          <div class="setting-item">
            <span class="setting-label">Threshold</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="threshold" min="-30" max="-10" step="1" value="${this.settings.threshold}">
              <span class="setting-value" id="thresholdValue">${this.settings.threshold}dB</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Ratio</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="ratio" min="2" max="8" step="0.5" value="${this.settings.ratio}">
              <span class="setting-value" id="ratioValue">${this.settings.ratio}:1</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Attack</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="attack" min="0.001" max="0.01" step="0.001" value="${this.settings.attack}">
              <span class="setting-value" id="attackValue">${this.settings.attack}s</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Release</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="release" min="0.05" max="0.2" step="0.01" value="${this.settings.release}">
              <span class="setting-value" id="releaseValue">${this.settings.release}s</span>
            </div>
          </div>
        </div>
      `;

      const dialog = this.createModernDialog('⚙️ Compressor Settings', content);
      this.setupSliderListeners(dialog);
      this.initializeSliderValues(dialog);
    }

    showAdvancedEngineDialog() {
      const content = `
        <div class="setting-group">
          <h4>🎭 Harmonic Enhancement</h4>
          <div class="setting-item">
            <span class="setting-label">Harmonic Intensity</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="harmonicIntensity" min="0" max="1" step="0.1" value="${this.settings.harmonicIntensity}">
              <span class="setting-value" id="harmonicIntensityValue">${this.settings.harmonicIntensity}</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Dynamic Range</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="dynamicRange" min="1" max="2" step="0.1" value="${this.settings.dynamicRange}">
              <span class="setting-value" id="dynamicRangeValue">${this.settings.dynamicRange}x</span>
            </div>
          </div>
        </div>

        <div class="setting-group">
          <h4>🧠 Psychoacoustic Processing</h4>
          <div class="setting-item">
            <span class="setting-label">Psycho Bass</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="psychoBass" min="0" max="1" step="0.1" value="${this.settings.psychoBass}">
              <span class="setting-value" id="psychoBassValue">${this.settings.psychoBass}</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Stereo Depth</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="stereoDepth" min="0" max="1" step="0.1" value="${this.settings.stereoDepth}">
              <span class="setting-value" id="stereoDepthValue">${this.settings.stereoDepth}</span>
            </div>
          </div>
        </div>

        <div class="setting-group">
          <h4>📊 Performance Metrics</h4>
          <div style="font-family: monospace; font-size: 12px; line-height: 1.6;">
            <strong>Initialization:</strong> ${this.performanceMetrics.initTime.toFixed(2)}ms<br>
            <strong>Updates:</strong> ${this.performanceMetrics.updateCount}<br>
            <strong>Avg Update Time:</strong> ${this.performanceMetrics.averageUpdateTime.toFixed(2)}ms<br>
            <strong>Active Nodes:</strong> ${this.activeNodes.size}<br>
          </div>
        </div>
      `;

      const dialog = this.createModernDialog('🚀 Advanced Audio Engine', content);
      this.setupSliderListeners(dialog);
      this.initializeSliderValues(dialog);
    }

    showFineControlsDialog() {
      const content = `
        <div class="setting-group">
          <h4>🎚️ Fine Audio Controls</h4>
          <div class="setting-item">
            <span class="setting-label">Audio Delay</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="delayTime" min="0.005" max="0.05" step="0.005" value="${this.settings.delayTime}">
              <span class="setting-value" id="delayTimeValue">${this.settings.delayTime}s</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Master Gain</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="gainValue" min="0.8" max="2.5" step="0.1" value="${this.settings.gainValue}">
              <span class="setting-value" id="gainValueValue">${this.settings.gainValue}x</span>
            </div>
          </div>
        </div>

        <div class="setting-group">
          <h4>🔧 System Controls</h4>
          <div class="setting-item">
            <span class="setting-label">Reset to Defaults</span>
            <div class="setting-control">
              <button class="preset-btn" onclick="audioProcessor.resetToDefaults()">🔄 Reset All</button>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Performance Test</span>
            <div class="setting-control">
              <button class="preset-btn" onclick="audioProcessor.runPerformanceTest()">⚡ Test</button>
            </div>
          </div>
        </div>
      `;

      const dialog = this.createModernDialog('🎚️ Fine Controls', content);
      this.setupSliderListeners(dialog);
      this.initializeSliderValues(dialog);
    }

    showCurrentSettings() {
      const content = `
        <div class="setting-group">
          <h4>📊 Current Audio Settings</h4>
          <div style="font-family: monospace; font-size: 12px; line-height: 1.6;">
            <strong>🎛️ Basic:</strong><br>
            • Delay: ${this.settings.delayTime}s<br>
            • Gain: ${this.settings.gainValue}x<br><br>

            <strong>🎚️ Compressor:</strong><br>
            • Threshold: ${this.settings.threshold}dB<br>
            • Ratio: ${this.settings.ratio}:1<br>
            • Attack: ${this.settings.attack}s<br>
            • Release: ${this.settings.release}s<br><br>

            <strong>🎭 Spatial:</strong><br>
            • Stereo Width: ${this.settings.stereoWidth}x<br>
            • Reverb: ${this.settings.reverbWet}<br><br>

            <strong>🎵 EQ:</strong><br>
            • Bass: ${this.settings.bassBoost}x<br>
            • Presence: ${this.settings.presenceBoost}x<br>
            • Treble: ${this.settings.trebleBoost}x<br>
          </div>
        </div>
      `;

      this.createModernDialog('📋 Audio Status', content);
    }

    setupSliderListeners(dialog) {
      const sliders = dialog.querySelectorAll('.setting-slider');

      sliders.forEach(slider => {
        const valueDisplay = dialog.querySelector(`#${slider.id}Value`);

        if (!valueDisplay) {
          console.warn(`Value display not found for slider: ${slider.id}`);
          return;
        }

        // Remove existing listeners to prevent duplicates
        if (slider._handleInput) {
          slider.removeEventListener('input', slider._handleInput);
        }

        // Create bound handler function
        const handleInput = (e) => {
          try {
            const value = parseFloat(e.target.value);
            const key = e.target.id;

            // Validate value
            if (isNaN(value)) {
              console.warn(`Invalid value for ${key}: ${e.target.value}`);
              return;
            }

            // Update display with proper formatting
            let displayValue = this.formatSliderValue(key, value);
            valueDisplay.textContent = displayValue;

            // Update setting and save
            this.settings[key] = value;
            GM_setValue(key, value);

            // Update audio nodes with debouncing
            clearTimeout(this.updateTimeout);
            this.updateTimeout = setTimeout(() => {
              this.updateAudioNodes();
            }, 100);

          } catch (error) {
            console.error(`Error handling slider input for ${e.target.id}:`, error);
          }
        };

        // Add event listener
        slider.addEventListener('input', handleInput);

        // Store reference for cleanup
        slider._handleInput = handleInput;
      });
    }

    // Helper method untuk format nilai slider
    formatSliderValue(key, value) {
      switch (key) {
        case 'delayTime':
        case 'attack':
        case 'release':
          return value.toFixed(3) + 's';
        case 'threshold':
          return value + 'dB';
        case 'ratio':
        case 'bassCompressorRatio':
          return value + ':1';
        case 'gainValue':
        case 'bassBoost':
        case 'subBassBoost':
        case 'midBassBoost':
        case 'trebleBoost':
        case 'presenceBoost':
        case 'stereoWidth':
          return value.toFixed(1) + 'x';
        case 'reverbWet':
          return value.toFixed(2);
        case 'stereoBalance':
          if (value > 0) return 'R' + value.toFixed(1);
          if (value < 0) return 'L' + Math.abs(value).toFixed(1);
          return 'Center';
        default:
          return value.toString();
      }
    }

    // Initialize slider values dan displays saat dialog dibuka
    initializeSliderValues(dialog) {
      const sliders = dialog.querySelectorAll('.setting-slider');

      sliders.forEach(slider => {
        const key = slider.id;
        const valueDisplay = dialog.querySelector(`#${key}Value`);

        if (valueDisplay && this.settings[key] !== undefined) {
          // Set slider value dari settings
          slider.value = this.settings[key];

          // Update display value
          const displayValue = this.formatSliderValue(key, this.settings[key]);
          valueDisplay.textContent = displayValue;

          // Trigger visual update
          slider.dispatchEvent(new Event('input', { bubbles: true }));
        }
      });
    }

    // Test method untuk memverifikasi semua slider berfungsi
    testSliders() {
      console.log('🧪 Testing Slider Functionality:');

      const testValues = {
        delayTime: 0.02,
        gainValue: 1.5,
        threshold: -18,
        ratio: 5,
        attack: 0.005,
        release: 0.1,
        stereoWidth: 2.0,
        reverbWet: 0.25,
        bassBoost: 1.4,
        trebleBoost: 1.3,
        presenceBoost: 1.2
      };

      Object.entries(testValues).forEach(([key, value]) => {
        const oldValue = this.settings[key];
        this.settings[key] = value;
        console.log(`✅ ${key}: ${oldValue} → ${value} (${this.formatSliderValue(key, value)})`);
      });

      this.updateAudioNodes();
      this.showNotification('Slider test completed - check console', 'info');
    }

    // Enhanced harmonic enhancement with advanced waveshaping algorithms
    createHarmonicCurve(intensity) {
      const samples = 44100;
      const curve = new Float32Array(samples);

      for (let i = 0; i < samples; i++) {
        const x = (i * 2) / samples - 1;

        // Advanced multi-stage harmonic generation
        const fundamental = Math.tanh(x * intensity);
        const secondHarmonic = 0.3 * Math.tanh(x * 2 * intensity);
        const thirdHarmonic = 0.15 * Math.tanh(x * 3 * intensity);
        const fifthHarmonic = 0.08 * Math.tanh(x * 5 * intensity);

        // Tube-like saturation curve for warmth
        const tubeSaturation = x / (1 + Math.abs(x * intensity));

        // Combine harmonics with tube saturation
        curve[i] = fundamental + secondHarmonic + thirdHarmonic + fifthHarmonic +
                   (0.2 * tubeSaturation * intensity);

        // Soft clipping to prevent harsh distortion
        curve[i] = Math.tanh(curve[i]);
      }

      return curve;
    }

    // Advanced audio analysis for adaptive processing
    setupAudioAnalysis() {
      if (!this.audioNodes.analyser) return;

      this.audioAnalysis = {
        frequencyData: new Uint8Array(this.audioNodes.analyser.frequencyBinCount),
        timeData: new Uint8Array(this.audioNodes.analyser.frequencyBinCount),
        bassLevel: 0,
        midLevel: 0,
        trebleLevel: 0,
        dynamicRange: 0,
        peakLevel: 0
      };

      // Start analysis loop
      this.startAudioAnalysis();
    }

    startAudioAnalysis() {
      if (!this.audioNodes.analyser || !this.audioAnalysis) return;

      const analyze = () => {
        try {
          // Get frequency and time domain data
          this.audioNodes.analyser.getByteFrequencyData(this.audioAnalysis.frequencyData);
          this.audioNodes.analyser.getByteTimeDomainData(this.audioAnalysis.timeData);

          // Analyze frequency bands
          this.analyzeFrequencyBands();

          // Calculate dynamic range
          this.calculateDynamicRange();

          // Adaptive processing based on analysis
          this.applyAdaptiveProcessing();

          // Continue analysis
          if (this.isInitialized) {
            requestAnimationFrame(analyze);
          }
        } catch (error) {
          console.warn('Audio analysis error:', error);
        }
      };

      analyze();
    }

    analyzeFrequencyBands() {
      const data = this.audioAnalysis.frequencyData;
      const binCount = data.length;

      // Calculate frequency band levels
      const bassEnd = Math.floor(binCount * 0.1);    // ~2.4kHz at 48kHz
      const midEnd = Math.floor(binCount * 0.4);     // ~9.6kHz at 48kHz

      let bassSum = 0, midSum = 0, trebleSum = 0;
      let peakLevel = 0;

      for (let i = 0; i < binCount; i++) {
        const level = data[i];
        peakLevel = Math.max(peakLevel, level);

        if (i < bassEnd) {
          bassSum += level;
        } else if (i < midEnd) {
          midSum += level;
        } else {
          trebleSum += level;
        }
      }

      // Normalize levels
      this.audioAnalysis.bassLevel = bassSum / bassEnd;
      this.audioAnalysis.midLevel = midSum / (midEnd - bassEnd);
      this.audioAnalysis.trebleLevel = trebleSum / (binCount - midEnd);
      this.audioAnalysis.peakLevel = peakLevel;
    }

    calculateDynamicRange() {
      const data = this.audioAnalysis.timeData;
      let min = 255, max = 0;

      for (let i = 0; i < data.length; i++) {
        min = Math.min(min, data[i]);
        max = Math.max(max, data[i]);
      }

      this.audioAnalysis.dynamicRange = max - min;
    }

    applyAdaptiveProcessing() {
      // Adaptive bass enhancement based on content
      if (this.audioAnalysis.bassLevel < 50 && this.audioNodes.subBassFilter) {
        // Boost bass for content with low bass
        const adaptiveBoost = 1 + (50 - this.audioAnalysis.bassLevel) / 100;
        const currentGain = this.audioNodes.subBassFilter.gain.value;
        const targetGain = currentGain * adaptiveBoost;

        // Smooth transition
        this.audioNodes.subBassFilter.gain.exponentialRampToValueAtTime(
          Math.min(targetGain, 20),
          this.audioCtx.currentTime + 0.1
        );
      }

      // Adaptive dynamic range processing
      if (this.audioAnalysis.dynamicRange < 30 && this.audioNodes.dynamic) {
        // Expand dynamics for compressed content
        const expansionRatio = 1.5 - (this.audioAnalysis.dynamicRange / 60);
        this.audioNodes.dynamic.ratio.exponentialRampToValueAtTime(
          Math.max(expansionRatio, 0.5),
          this.audioCtx.currentTime + 0.1
        );
      }
    }

    // Enhanced stereo depth modulation with error handling
    modulateStereoDepth(depth) {
      try {
        if (!this.audioCtx || !this.audioNodes.stereoDepth) {
          console.warn('Cannot modulate stereo depth - nodes not available');
          return;
        }

        const oscillator = this.audioCtx.createOscillator();
        const depthGain = this.audioCtx.createGain();

        oscillator.frequency.value = 0.1; // Slow modulation
        depthGain.gain.value = depth;

        oscillator.connect(depthGain);
        depthGain.connect(this.audioNodes.stereoDepth.pan);

        oscillator.start();
        setTimeout(() => {
          try {
            oscillator.stop();
          } catch (error) {
            console.warn('Error stopping stereo depth oscillator:', error);
          }
        }, 50); // Short modulation
      } catch (error) {
        console.error('Error in stereo depth modulation:', error);
      }
    }

    // Performance testing method
    async runPerformanceTest() {
      try {
        const testResults = {
          initTime: this.performanceMetrics.initTime,
          averageUpdateTime: this.performanceMetrics.averageUpdateTime,
          updateCount: this.performanceMetrics.updateCount,
          activeNodes: this.activeNodes.size,
          memoryUsage: performance.memory ? {
            used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
            total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024)
          } : 'Not available'
        };

        console.log('🚀 Performance Test Results:', testResults);

        // Test audio processing latency
        const latencyTest = await this.testAudioLatency();
        testResults.audioLatency = latencyTest;

        this.showNotification(`Performance: Init ${testResults.initTime.toFixed(1)}ms, Update ${testResults.averageUpdateTime.toFixed(1)}ms`, 'info');

        return testResults;
      } catch (error) {
        console.error('Performance test failed:', error);
        this.showNotification('Performance test failed', 'error');
      }
    }

    async testAudioLatency() {
      try {
        if (!this.audioCtx || !this.audioNodes.analyser) {
          return 'Not available';
        }

        const startTime = this.audioCtx.currentTime;

        // Create test tone
        const oscillator = this.audioCtx.createOscillator();
        const gain = this.audioCtx.createGain();

        oscillator.frequency.value = 440;
        gain.gain.value = 0.01; // Very quiet

        oscillator.connect(gain);
        gain.connect(this.audioNodes.analyser);

        oscillator.start();
        oscillator.stop(startTime + 0.1);

        const endTime = this.audioCtx.currentTime;
        const latency = (endTime - startTime) * 1000; // Convert to ms

        return `${latency.toFixed(2)}ms`;
      } catch (error) {
        console.error('Audio latency test failed:', error);
        return 'Test failed';
      }
    }

    // Reset to default settings with confirmation
    resetToDefaults() {
      try {
        const defaultSettings = {
          delayTime: DEFAULT_DELAY,
          gainValue: DEFAULT_GAIN,
          threshold: DEFAULT_THRESHOLD,
          ratio: DEFAULT_RATIO,
          attack: DEFAULT_ATTACK,
          release: DEFAULT_RELEASE,
          reverbWet: DEFAULT_REVERB_WET,
          subBassBoost: DEFAULT_SUB_BASS_BOOST,
          midBassBoost: DEFAULT_MID_BASS_BOOST,
          bassBoost: DEFAULT_BASS_BOOST,
          bassCompressorRatio: DEFAULT_BASS_COMPRESSOR_RATIO,
          trebleBoost: DEFAULT_TREBLE_BOOST,
          presenceBoost: DEFAULT_PRESENCE_BOOST,
          stereoWidth: DEFAULT_STEREO_WIDTH,
          stereoBalance: DEFAULT_STEREO_BALANCE,
          harmonicIntensity: DEFAULT_HARMONIC_INTENSITY,
          dynamicRange: DEFAULT_DYNAMIC_RANGE,
          psychoBass: DEFAULT_PSYCHO_BASS,
          stereoDepth: DEFAULT_STEREO_DEPTH
        };

        // Apply default settings
        Object.entries(defaultSettings).forEach(([key, value]) => {
          this.settings[key] = value;
          GM_setValue(key, value);
        });

        // Clear cached curves and buffers to force regeneration
        this.harmonicCurve = null;
        this.reverbBuffer = null;
        this.lastHarmonicIntensity = null;

        // Update audio nodes
        this.updateAudioNodes();

        this.showNotification('🔄 All settings reset to defaults', 'success');
        console.log('Audio settings reset to defaults');
      } catch (error) {
        console.error('Error resetting to defaults:', error);
        this.showNotification('❌ Failed to reset settings', 'error');
      }
    }

    // Memory management methods
    clearMemoryCaches() {
      try {
        // Clear cached audio buffers
        this.reverbBuffer = null;
        this.harmonicCurve = null;
        this.lastHarmonicIntensity = null;

        // Clear analysis data
        if (this.audioAnalysis) {
          this.audioAnalysis.frequencyData = null;
          this.audioAnalysis.timeData = null;
        }

        // Update memory monitor
        if (this.memoryMonitor) {
          this.memoryMonitor.memoryUsage.audioBuffers = 0;
          this.memoryMonitor.memoryUsage.nodeCount = this.activeNodes.size;
        }

        console.log('Memory caches cleared');
      } catch (error) {
        console.error('Error clearing memory caches:', error);
      }
    }

    optimizeMemoryUsage() {
      try {
        // Remove unused nodes from pool
        this.nodePool.forEach((nodes, type) => {
          if (nodes.length > 5) { // Keep max 5 nodes per type
            nodes.splice(5).forEach(node => {
              try {
                if (node && typeof node.disconnect === 'function') {
                  node.disconnect();
                }
              } catch (error) {
                console.warn('Error disconnecting pooled node:', error);
              }
            });
          }
        });

        // Clear weak references to removed DOM elements
        this.cleanupWeakReferences();

        // Force garbage collection hint
        if (window.gc) {
          window.gc();
        }

        console.log('Memory usage optimized');
      } catch (error) {
        console.error('Error optimizing memory usage:', error);
      }
    }

    cleanupWeakReferences() {
      // Clean up weak references to DOM elements that may have been removed
      const elementsToCheck = document.querySelectorAll('[data-audio-enhanced]');
      const validElements = new Set(elementsToCheck);

      // Remove references to elements no longer in DOM
      this.eventListeners.forEach((listener, key) => {
        if (listener.element && !validElements.has(listener.element)) {
          this.eventListeners.delete(key);
        }
      });
    }

    getMemoryReport() {
      const report = {
        audioNodes: this.activeNodes.size,
        eventListeners: this.eventListeners.size,
        nodePool: Array.from(this.nodePool.values()).reduce((sum, nodes) => sum + nodes.length, 0),
        cachedBuffers: {
          reverb: !!this.reverbBuffer,
          harmonic: !!this.harmonicCurve
        },
        performanceMetrics: this.performanceMetrics
      };

      if (this.memoryMonitor) {
        report.systemMemory = this.memoryMonitor.getReport();
      }

      return report;
    }

    // Enhanced cleanup with memory optimization
    async enhancedCleanup() {
      try {
        // Stop visualizer
        this.visualizerActive = false;

        // Clear memory caches
        this.clearMemoryCaches();

        // Optimize memory usage
        this.optimizeMemoryUsage();

        // Destroy memory monitor
        if (this.memoryMonitor) {
          this.memoryMonitor.destroy();
          this.memoryMonitor = null;
        }

        // Call standard cleanup
        await this.cleanup();

        console.log('Enhanced cleanup completed');
      } catch (error) {
        console.error('Error in enhanced cleanup:', error);
      }
    }
  }

  // Create global audio processor instance
  const audioProcessor = new AudioProcessor();

  // === STREAMLINED MENU - Essential Controls Only ===
  
  // Enhanced Audio Processing
  GM_registerMenuCommand("✨ Master Audio Controls", () => {
    audioProcessor.showSpatialAudioDialog();
  });

  GM_registerMenuCommand("🎛️ Advanced Audio Engine", () => {
    audioProcessor.showAdvancedEngineDialog();  // New dialog for harmonics/psychoacoustics
  });

  // Separator
  GM_registerMenuCommand("─────────────────", () => {});

  // Enhanced Presets System
  GM_registerMenuCommand("🎭 Dolby Balanced", () => {
    audioProcessor.applyPreset('dolby');
  });

  GM_registerMenuCommand("🎪 Crystal Clear", () => {
    audioProcessor.applyPreset('superStereo');
  });

  GM_registerMenuCommand("💫 Dynamic Bass", () => {  // Renamed for clarity
    audioProcessor.applyPreset('bassBoost');
  });

  GM_registerMenuCommand("🎧 Studio Monitor", () => {  // Updated name
    audioProcessor.applyPreset('headphone');
  });

  GM_registerMenuCommand("🏛️ Concert Hall", () => {
    audioProcessor.applyPreset('concertHall');
  });

  // Separator
  GM_registerMenuCommand("─────────────────", () => {});

  // Settings & Tools
  GM_registerMenuCommand("⚙️ Basic Settings", () => {
    audioProcessor.showBasicAudioDialog();
  });

  GM_registerMenuCommand("🔧 Compressor & Dynamics", () => {
    audioProcessor.showAdvancedDialog();
  });

  GM_registerMenuCommand("🎚️ Fine Tuning", () => {
    audioProcessor.showFineControlsDialog();  // New dialog for detailed adjustments
  });

  // Separator
  GM_registerMenuCommand("─────────────────", () => {});

  // Utility Commands
  GM_registerMenuCommand("📊 Status & Info", () => {
    audioProcessor.showCurrentSettings();
  });

  GM_registerMenuCommand("💾 Save Settings", () => {
    audioProcessor.saveAllSettings();
    audioProcessor.showNotification('Audio settings saved successfully', 'success');
  });

  GM_registerMenuCommand("🔄 Reset All", () => {
    if (confirm("Reset all audio settings to default?")) {
      audioProcessor.resetToDefaults();
      audioProcessor.showNotification('All settings reset to default values', 'info');
    }
  });

  // Optimized initialization with better error handling
  async function initializeScript() {
    try {
      await audioProcessor.init();

      // Setup navigation change detection for SPA behavior
      let lastUrl = location.href;
      const observer = new MutationObserver(() => {
        if (location.href !== lastUrl) {
          lastUrl = location.href;
          // Reinitialize on navigation change with delay
          setTimeout(async () => {
            try {
              await audioProcessor.init();
            } catch (error) {
              console.error("Failed to reinitialize on navigation:", error);
            }
          }, 1000);
        }
      });

      observer.observe(document.body, { childList: true, subtree: true });

      // Enhanced cleanup on page unload
      const cleanup = () => {
        try {
          observer.disconnect();
          audioProcessor.cleanup();
        } catch (error) {
          console.error("Error during cleanup:", error);
        }
      };

      window.addEventListener('beforeunload', cleanup);
      window.addEventListener('pagehide', cleanup);

    } catch (error) {
      console.error("Failed to initialize YouTube Audio Processor:", error);
      // Show user-friendly error notification
      if (typeof audioProcessor.showNotification === 'function') {
        audioProcessor.showNotification('❌ Audio enhancement failed to initialize', 'error');
      }
    }
  }

  // Initialize when DOM is ready
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", initializeScript);
  } else {
    initializeScript();
  }
})();